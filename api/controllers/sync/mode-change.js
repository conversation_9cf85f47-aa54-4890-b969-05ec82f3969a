const self = require('../../services/sync/sync.service');
const globalHelper = require('../../utils/globalhelper');
const selfUtils = require('../../utils/sync/mode-change.util');

module.exports = {

  friendlyName: 'Mode-Change',
  description: 'When modes are changed it finds the relevant recipes and issues a play/pause command.',

  example: [

  ],

  inputs: {
    controllerId: {
      type: 'string',
      example: '1234'
    },
    siteId: {
      type: 'string',
      example: 'ssh',
      required: true
    },
    modes: {
      type: {},
      example: {'did.param': 'joulerecipe'},
      required: true
    },
    secret: {
      type: 'string',
      example: 'sha-256 code',
      required: true
    }
  },

  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[sync > mode-change] Server Error!',
    },
    badRequest: {
      responseType: 'badRequest',
      description: '[sync > mode-change] Bad Request!',
    },
    forbidden: {
      responseType: 'forbidden',
      description: '[sync > mode-change] forbidden Request!',
    }
  },

  fn: async function (inputs, exits) {
    try {
      let { siteId, modes, secret } = inputs;

      if (secret !== '41f5e8d3dbae45b33f4ce040ff4ce13e23a5ddd4') return exits.badRequest({ problems: ['Invalid secret passed'] });

      if(globalHelper.isNullish(modes)) return exits.success();

      let recipes = await self.getAllDeployedRecipesOfASite(siteId);
      if(recipes.length === 0) return exits.success();

      recipes = selfUtils.removePausedRecipes(recipes);
      if(recipes.length === 0) return exits.success();

      let controllerRecipeMap = selfUtils.getAffectedRecipesMap(recipes, modes);

      if(globalHelper.isNullish(controllerRecipeMap)) return exits.success();

      controllerRecipeMap = selfUtils.getRecipePacketForNewModes(controllerRecipeMap, modes);

      if(controllerRecipeMap.server) {
        await self.handleServerRecipes(controllerRecipeMap.server, siteId);
        delete controllerRecipeMap.server;
      }

      if(globalHelper.isNullish(controllerRecipeMap)) return exits.success();

      await self.updateRecipeStateInSyncTable(controllerRecipeMap);

      await self.sendStartStopToControllers(siteId, controllerRecipeMap);

      exits.success();
    } catch (e) {
      sails.log.error('Error in mode-change: ' + e);
      return exits.serverError(e);
    }
  }
};
