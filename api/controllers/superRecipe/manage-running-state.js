const recipeService = require('../../services/superRecipe/recipe.public');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');
module.exports = {
  friendlyName: 'manage-running-state',
  description: 'Manage Running State',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    status: {
      type: 'number',
      required: true,
      example: 1,
    },
    recipeType: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> manage-running-state] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> manage-running-state] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> manage-running-state] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> manage-running-state] Not Found',
    },
    entityUnprocessable: {
      statusCode: 422
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> manage-running-state] Request Registered Successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        id,
        siteId,
        status,
        recipeType
      } = inputs;

      if (!id || !siteId || ![0, 1].includes(status)) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }

      if (!['action', 'alert'].includes(recipeType)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const recipeData = await recipeService.getActionRecipeInfoById(id, recipeType, siteId);

      if (+recipeData?.['is_deployed'] !== 1) {
        return exits.badRequest({ problems: ['Recipe is not deployed'] });
      }
      const runOn = siteId === 'ash-tri' ? 8772 : recipeData.run_on;
      await RecipeIotCommunicationInterface.updateRunningState(siteId, runOn, status, recipeData?.rid);
      return exits.success(recipeData);
    } catch (error) {
      sails.log.error('[superRecipe -> manage-running-state]', error);
      if (error?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
        return exits.entityUnprocessable({
          message: error?.message,
          code: error?.code
        });
      }
      return exits.serverError(error);
    }

  }
};
