const recipeService = require('../../services/superRecipe/recipe.public');
const RecipeIotPayloadBuilder = require('../../services/superRecipe/lib/recipe.iot.payload.builder');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');

module.exports = {
  friendlyName: 'deploy-super-recipe',
  description: 'deploy-super-recipe',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    recipeType: {
      type: 'string',
      required: true,
      example: 'action',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> deploy-super-recipe] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> deploy-super-recipe] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> deploy-super-recipe] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> deploy-super-recipe] Not Found',
    },
    entityUnprocessable: {
      statusCode: 422
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> deploy-super-recipe] Request Registered successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        id,
        siteId,
        recipeType,
      } = inputs;

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }
      if (!['action', 'alert'].includes(recipeType)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const recipeData = await recipeService.getActionRecipeInfoById(id, recipeType, siteId);
      if (_.isEmpty(recipeData?.schedules)) {
        return exits.badRequest({ message: 'There is no active schedules for this recipe ' + id });
      }

      const builder = new RecipeIotPayloadBuilder(recipeData);
      const payload = builder.buildPayload();
      if (siteId === 'ash-tri') {
        await RecipeIotCommunicationInterface.deploySuperRecipe(siteId, 8772, payload);
      }
      return exits.success({
        message: `Recipe ${id} deploy request registered successfully`,
        payload,
        runOn: recipeData?.run_on
      });
    } catch (error) {
      sails.log.error('[superRecipe -> deploy-super-recipe]', error);
      if (error?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
        return exits.entityUnprocessable({
          message: error?.message,
          code: error?.code
        });
      }
      return exits.serverError(error);
    }

  }
};
