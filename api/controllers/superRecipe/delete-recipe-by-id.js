const recipeService = require('../../services/superRecipe/recipe.public');
const scheduleService = require('../../services/superRecipe/schedule/schedule.public');
const RecipeIotCommunicationInterface = require('../../services/superRecipe/lib/recipe.iot.communication.interface');

module.exports = {
  friendlyName: 'delete-recipe-by-id',
  description: 'Deletes recipe by id',
  inputs: {
    siteId: {
      type: 'string',
      required: true,
      example: 'gob-coi',
    },
    id: {
      type: 'string',
      required: true,
      example: '123',
    },
    type: {
      type: 'string',
      required: true,
      example: 'action/alert',
    }
  },
  exits: {
    serverError: {
      responseType: 'serverError',
      description: '[superRecipe -> delete-recipe-by-id] Server Error!',
    },
    badRequest: {
      statusCode: 400,
      responseType: 'badRequest',
      description: '[superRecipe -> delete-recipe-by-id] Bad Request!',
    },
    forbidden: {
      statusCode: 403,
      responseType: 'forbidden',
      description: '[superRecipe -> delete-recipe-by-id] forbidden Request!',
    },
    notFound: {
      statusCode: 404,
      description: '[superRecipe -> delete-recipe-by-id] Not Found',
    },
    success: {
      statusCode: 200,
      description: '[superRecipe -> delete-recipe-by-id] Recipe Deleted successfully',
    },
  },
  fn: async (inputs, exits) => {
    try {
      const {
        id,
        siteId,
        type
      } = inputs;

      if (!id || !siteId) {
        return exits.badRequest({ message: 'id/siteId is required' });
      }
      if (!['action', 'alert'].includes(type)) {
        return exits.badRequest({ message: 'Type of recipe can be action or alert only' });
      }

      const recipeData = await recipeService.getActionRecipeInfoById(id, type, siteId);
      if (recipeData?.error) {
        return exits.badRequest({ message: 'Recipe not found' });
      }
      if (recipeData?.is_deployed === 1) {
        /**Recipe is deployed, so only trigger removal from IOT*/
        await RecipeIotCommunicationInterface.deleteSuperRecipeFromController(
          siteId,
          8772,
          recipeData.rid
        );

        return exits.success({
          message: `Recipe is deployed. Request to remove from IOT controller has been initiated.`,
          runOn: recipeData.run_on
        });
      }

      await Promise.all([
        recipeService.delete(id),
        recipeService.delete_children_recipes({ parent_recipe_id: id }),
        scheduleService.delete({ recipe_id: id })
      ]);

      return exits.success({
        message: `Recipe with id=${id} deleted successfully.`,
        runOn: recipeData.run_on
      });
    } catch (error) {
      sails.log.error('[superRecipe -> delete-recipe-by-id]', error);
      if (error.code === 'E_NOT_FOUND') {
        return exits.badRequest({ message: error.message });
      }
      return exits.serverError(error);
    }

  }
};
