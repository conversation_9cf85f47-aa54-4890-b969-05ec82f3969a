const ComponentService = require('../../services/component/component.service');
const InputValidator = require('../../utils/component/inputValidation');
const auditEventLogService = require('../../services/auditEventLog/auditEventLog.public');

module.exports = {
  friendlyName: 'ChangeModeOfAssetControl',
  description: 'Change the Operating mode of command at an asset',
  example: [],
  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: {
        id: 'userId',
        _role: 'role',
        _site: 'siteId'
      },
      description: 'User meta information added by default to authenticated routes',
    },
    componentId: {
      type: 'string',
      required: true,
    },
    modeChangeDetail: {
      type: 'ref',
      columnType: [{
        controlAbbr: 'chillerSetPoint',
        mode: 'recipe'
      }],
      required: true,
    },
    assetModeLabel: {
      type: 'string',
      required: true,
    }
  },
  exits: {
    success: {
      statusCode: 202,
      description: 'Mode change requested has been forwarded to IoT'
    },
    serverError: {
      statusCode: 500,
      responseType: 'serverError',
      description: 'Server Error',
    },
    notFound: {
      statusCode: 404,
      description: 'Not Found',
    },
    modeChangeRequested: {
      statusCode: 202,
      description: 'Mode change requested',
    },
    badRequest: {
      statusCode: 400,
      description: 'Bad Request',
    },
    entityUnprocessable: {
      statusCode: 422
    },
  },
  fn: async function (inputs, exits) {
    try {
      const {
        componentId,
        modeChangeDetail,
        assetModeLabel,
        _userMeta: {
          id: userId,
          _site: siteId
        },
      } = inputs;

      InputValidator.validateModeChangeRequest({
        modeChangeDetail,
        assetModeLabel
      });
      const result = await ComponentService.changeAssetControlMode({
        modeChangeDetail,
        componentId,
        assetModeLabel
      });

      const auditPayload = {
        event_name: 'state_update_mode_change',
        user_id: userId,
        site_id: siteId,
        asset_id: componentId,
        req: this.req,
        prev_state: {
          modeChangeDetail,
          assetModeLabel
        },
        curr_state: Object.assign({}, result),
      };

      auditEventLogService.emit(auditPayload);

      return exits.success(result);
    } catch (err) {
      switch (err.code) {
        case 'E_COMPONENT_NOT_FOUND': {
          return exits.badRequest({
            err: err.message
          });
        }
        case 'E_INPUT_ERROR': {
          return exits.badRequest({
            err: err.message
          });
        }
        case 'E_INVALID_CONTROL': {
          return exits.badRequest({
            err: err.message
          });
        }
        case 'E_CONTROL_COMMAND_NOT_EXIST': {
          return exits.notFound({
            err: err.message,
            data: err.data
          });
        }
        case 'E_INPUT_VALIDATION': {
          return exits.badRequest({
            err: err.message
          });
        }
        case 'E_UNSUPPORTED_ASSET': {
          return exits.badRequest({
            err: err.message,
            data: err.data
          });
        }
        case 'E_CONTROLLER_ID_NOT_FOUND': {
          return exits.badRequest({
            err: err.message
          });
        }
        case 'E_DEVICE_NOT_FOUND': {
          return exits.badRequest({
            err: err.message
          });
        }
        default: {
          sails.log.error('Component > ChangeModeOfAssetControl', err);
          sails.log.error(err);
          if (err?.code?.includes('[AWS-IOT-CORE-SERVICE')) {
            return exits.unprocessableEntity({
              message: err?.message,
              code: err?.code
            });
          }
          return exits.serverError(err);
        }
      }
    }
  },
};
