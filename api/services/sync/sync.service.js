const recipeService = require('../recipe/recipe.public');
const syncService = require('./sync.private');

exports = {
  find: syncService.find,
  findOne: syncService.findOne,
  create: syncService.create,
  update: syncService.update,
  destroy: syncService.destroy,

  /**
   * :: Joule Recipe Service
   * @param {*} siteId 
   */
  getAllDeployedRecipesOfASite: async function (siteId) {
    try {
      return await recipeService.getAllDeployedRecipesOfASite(siteId);
    } catch (e) {
      sails.log.error('Error in syncService.getAllDeployedRecipesOfASite: ', e);
      throw e;
    }
  },
  
  /**
   * :: Joule Recipe Service
   * @param {*} siteId 
   * @param {*} controllerRecipeMap 
   */
  sendStartStopToControllers: async function (siteId, controllerRecipeMap) {
    try {
      return await recipeService.sendStartStopToControllers(siteId, controllerRecipeMap);
    } catch (e) {
      sails.log.error('Error in syncService.sendStartStopToControllers: ', e);
      throw e;
    }
  },

  /**
   * Given the controller-recipe mapping it updates the recipe state in sync table
   * @param {Object} controllerRecipeMap {1234: [{rid: '2345-dfdv-3r23r', switchOff: '2'}]}
   */
  updateRecipeStateInSyncTable: async function(controllerRecipeMap) {
    let $updateStatePromises = [];
    for(let controllerId in controllerRecipeMap) {
      for(let eachRecipe of controllerRecipeMap[controllerId]) {
        let type = `recipe_${eachRecipe.rid}`;
        let switchOff = eachRecipe.switchOff;
        $updateStatePromises.push(this.update({controllerId, type}, {switchOff}));
      }
    }
    await Promise.allSettled($updateStatePromises);
  },

  /**
   * Given the list of recipes running on server, it directly updates the recipe table
   * @param {Array} recipes [{rid: '2345-dfdv-3r23r', switchOff: '2'}]
   * @param {string} siteId Site ID
   */
  handleServerRecipes: async function(recipes, siteId) {
    let $recipeUpdatePromises = [];
    for (let eachRecipe of recipes) {
      let rid = eachRecipe.rid;
      let switchOff = eachRecipe.switchOff;
      $recipeUpdatePromises.push(recipeService.startStopRecipe(siteId, rid, switchOff));
    }
    await Promise.allSettled($recipeUpdatePromises);
  },

  destroyRecipeState: async function(controllerId, recipeId) {
    try {
      let type = `recipe_${recipeId}`;
      await syncService.destroy({controllerId, type});
    } catch (e) {
      sails.log.error('sync.service::destroyRecipeState ',e);
      throw (e);
    }
  }
};

module.exports = exports;
