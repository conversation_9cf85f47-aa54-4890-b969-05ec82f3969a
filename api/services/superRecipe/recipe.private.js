const flaverr = require('flaverr');
const componentService = require('../component/component.public');
const { extractDaysOfWeekFromCron } = require('./lib/cron.builder');

module.exports = {
  /**
   * Find records from the main RecipeInfo table.
   * @param {Object} criteria - The criteria for finding records.
   * @returns {Promise<Array>} The list of matching RecipeInfo records.
   */
  async find(criteria = {}) {
    // eslint-disable-next-line no-return-await,no-undef
    return await RecipeInfo.find(criteria);
  },

  /**
   * Find a single record from the main RecipeInfo table.
   * @param {Object} criteria - The criteria for finding a record.
   * @returns {Promise<Object>} The matching RecipeInfo record.
   */
  async findOne(criteria = {}) {
    return await RecipeInfo.findOne(criteria);
  },

  /**
   * Create a new record in the main RecipeInfo table.
   * @param {Object} data - The data for the new record.
   * @returns {Promise<Object>} The created RecipeInfo record.
   */
  async create(data) {
    return await RecipeInfo.create(data)
      .fetch();
  },

  /**
   * @description Update records in the main RecipeInfo table inside a transaction.
   * @param {Object} criteria - The criteria to find the records to update.
   * @param {Object} data - The data to update.
   * @returns {Promise<Array>} The updated RecipeInfo records.
   */
  async update(criteria, data) {
    const { children_recipes = [] } = data;

    if (_.isEmpty(children_recipes)) {
      throw flaverr('E_INPUT_VALIDATION', new Error('Provide valid children_recipes with ids'));
    }

    await _updateSuperRecipe({ ...data, ...criteria });
  },

  /**
   * Delete records from the main RecipeInfo table.
   * @param {Object} criteria - The criteria to find the records to delete.
   * @returns {Promise<Array>} The deleted RecipeInfo records.
   */
  async delete(criteria) {
    const deletedRecords = await RecipeInfo.update(criteria)
      .set({ status: 0 })
      .fetch()
      .fetch();
    if (deletedRecords.length === 0) throw flaverr('E_NOT_FOUND', new Error('No RecipeInfo records found to delete'));
    return deletedRecords;
  },
  /**
   * @description Fetches a recipe along with its related data from child tables `recipe_actions` and `children_recipes`.
   * @async
   * @function getRecipeWithChildData
   * @param {number} recipeId - The ID of the recipe to fetch.
   * @param {string} type - Optional Key
   * @param site_id
   * @returns {Promise<Object>} The recipe data, including its associated `recipe_actions` and `children_recipes`.
   * @throws {Error} If the recipe is not found or an error occurs during the query.
   */
  async getActionRecipeInfoById(recipeId, type = 'action', site_id) {
    const recipeInfo = await RecipeInfo.findOne({
      id: recipeId,
      status: 1,
      recipe_type: type,
      site_id,
      is_recipe_template: 0
    })
      .populate('children_recipes', {
        where: { status: 1 },
      })
      .populate('schedules', {
        where: { status: 1 },
      });

    if (!recipeInfo) {
      return { error: 'Recipe not found' };
    }

    if (recipeInfo.children_recipes && recipeInfo.children_recipes.length > 0) {
      for (const child of recipeInfo.children_recipes) {
        child.actions = await RecipeActions.find({
          recipe_id: child.id,
          status: 1
        });
      }
    }

    recipeInfo.schedules = _processSchedules(recipeInfo.schedules);

    return recipeInfo;
  },
  /**
   * @description Fetches a recipe template along with its related data from child tables `recipe_actions` and `children_recipes`.
   * @async
   * @function getRecipeTemplateWithChildData
   * @param {number} recipeId - The ID of the recipe template to fetch.
   * @returns {Promise<Object>} The recipe template data, including its associated `recipe_actions` and `children_recipes`.
   * @throws {Error} If the recipe template is not found or an error occurs during the query.
   */
  async getActionRecipeTemplateInfoById(recipeId) {
    const recipeInfo = await RecipeInfo.findOne({
      id: recipeId,
      status: 1,
      is_recipe_template: 1
    })
      .populate('children_recipes', {
        where: { status: 1 },
      });

    if (!recipeInfo) {
      return { error: `No Recipe Template Info Found for id=${recipeId}` };
    }

    if (!_.isEmpty(recipeInfo.children_recipes)) {
      recipeInfo.children_recipes = await Promise.all(
        recipeInfo.children_recipes.map(async (child) => {
          const childActionData = await RecipeActions.find({
            recipe_id: child.id,
            status: 1,
          });

          const actions = await Promise.all(
            childActionData.map(async (action) => ({
              execution_order: action.execution_order,
              value: action.value,
              command: action.command,
              parentDeviceType: await componentService.getDeviceTypeByComponentId(action.parent, recipeInfo.site_id),
            }))
          );

          const params = {};
          if (child.params) {
            await Promise.all(
              Object.keys(child.params)
                .map(async (param) => {
                  const deviceId = child.params[param].split('.')[0];
                  const deviceType = await componentService.getDeviceTypeByComponentId(deviceId, recipeInfo.site_id);
                  params[param] = child.params[param].replace(deviceId, deviceType);
                })
            );
          }

          return {
            actions,
            expression_template: child.expression_template,
            observation_time: child.observation_time,
            params,
            formula: child.formula
          };
        })
      );
    }

    const templateUsedCount = +(await _getRecipeTemplateUsedCount(recipeInfo?.id));
    return {
      children_recipes: recipeInfo.children_recipes,
      site_id: recipeInfo.site_id,
      app_type: recipeInfo.app_type,
      components_type: recipeInfo.components_type,
      title: recipeInfo.title,
      description: recipeInfo.description,
      priority: recipeInfo.priority,
      notify: recipeInfo.notify,
      templateUsedCount,
      id: recipeInfo.id
    };
  },
  /**
   * @description Fetches a recipes along with its related data from child tables `recipe_actions` and `children_recipes`.
   * @async
   * @param {string}  siteId
   * @param recipeType
   * @function getRecipesWithChildData
   * @returns {Promise<Object>} The recipe data, including its associated `recipe_actions` and `children_recipes`.
   * @throws {Error} If the recipe is not found or an error occurs during the query.
   */
  async fetchAllSuperRecipesBySiteId(siteId, recipeType) {
    const recipes = await RecipeInfo.find({
      status: 1,
      site_id: siteId,
      is_recipe_template: 0,
      recipe_type: recipeType,
    })
      .populate('children_recipes', {
        where: { status: 1 },
      })
      .populate('schedules', {
        where: { status: 1 },
      });

    if (_.isEmpty(recipes)) {
      return [];
    }

    for (const recipe of recipes) {
      recipe.schedules = _processSchedules(recipe.schedules);
    }

    return recipes;
  },
  async fetchSuperRecipesById({
    id,
    recipeType,
    siteId
  }) {
    const recipes = await RecipeInfo.find({
      status: 1,
      site_id: siteId,
      id,
      is_recipe_template: 0,
      recipe_type: recipeType,
    })
      .populate('children_recipes', {
        where: { status: 1 },
      })
      .populate('schedules', {
        where: { status: 1 },
      });
    return _.isEmpty(recipes) ? null : recipes[0];
  },

  getRecipeTemplateUsedCount: _getRecipeTemplateUsedCount,
  /**
   * @description Fetches a recipe template along with its related data from child tables `recipe_actions` and `children_recipes`.
   * @async
   * @param {string}  siteId
   * @param {string} type recipe type
   * @function getRecipesWithChildData
   * @returns {Promise<Object>} The recipe template data, including its associated `recipe_actions` and `children_recipes`.
   * @throws {Error} If the recipe template is not found or an error occurs during the query.
   */
  fetchAllSuperRecipeTemplatesBySiteId: async function (siteId, type) {
    const recipes = await RecipeInfo.find({
      status: 1,
      is_recipe_template: 1,
      recipe_type: type,
    })
      .populate('children_recipes', {
        where: { status: 1 },
      });

    if (_.isEmpty(recipes)) {
      return [];
    }

    const recipeTemplates = [];
    for (const recipe of recipes) {
      if (recipe.children_recipes && recipe.children_recipes.length > 0) {
        recipe.children_recipes = await Promise.all(
          recipe.children_recipes.map(async (child) => {
            const childActionData = await RecipeActions.find({
              recipe_id: child.id,
            });

            const actions = await Promise.all(
              childActionData.map(async (action) => ({
                execution_order: action.execution_order,
                value: action.value,
                command: action.command,
                parentDeviceType: await componentService.getDeviceTypeByComponentId(action.parent, siteId),
              }))
            );

            const params = {};
            if (child.params) {
              await Promise.all(
                Object.keys(child.params)
                  .map(async (param) => {
                    const deviceId = child.params[param].split('.')[0];
                    const deviceType = await componentService.getDeviceTypeByComponentId(deviceId, siteId);
                    params[param] = child.params[param].replace(deviceId, deviceType);
                  })
              );
            }

            return {
              actions: type === 'action' ? actions : undefined,
              description: type === 'alert' ? child?.description : undefined,
              expression_template: child.expression_template,
              observation_time: child.observation_time,
              params,
              formula: child.formula
            };
          })
        );
      }

      const templateUsedCount = +(await _getRecipeTemplateUsedCount(recipe?.id));

      recipeTemplates.push({
        children_recipes: recipe.children_recipes,
        site_id: recipe.site_id,
        app_type: recipe.app_type,
        components_type: recipe.components_type,
        title: recipe.title,
        description: recipe.description,
        priority: recipe.priority,
        notify: recipe.notify,
        templateUsedCount,
        id: recipe.id
      });
    }

    return recipeTemplates;
  },
  /**
   * Find records from the RecipeActions table.
   * @param {Object} criteria - The criteria for finding RecipeActions records.
   * @returns {Promise<Array>} The list of matching RecipeActions records.
   */
  async find_recipe_actions(criteria = {}) {
    return await RecipeActions.find(criteria);
  },

  /**
   * Create a new record in the RecipeActions table.
   * @param {Object} data - The data for the new RecipeActions record.
   * @returns {Promise<Object>} The created RecipeActions record.
   */
  async create_recipe_actions(data) {
    return await RecipeActions.create(data)
      .fetch();
  },

  /**
   * Update records in the RecipeActions table.
   * @param {Object} criteria - The criteria to find the RecipeActions records to update.
   * @param {Object} data - The data to update.
   * @returns {Promise<Array>} The updated RecipeActions records.
   */
  async update_recipe_actions(criteria, data) {
    const updatedRecords = await RecipeActions.update(criteria)
      .set(data)
      .fetch();
    return updatedRecords;
  },

  /**
   * Delete records from the RecipeActions table.
   * @param {Object} criteria - The criteria to find the RecipeActions records to delete.
   * @returns {Promise<Array>} The deleted RecipeActions records.
   */
  async delete_recipe_actions(criteria) {
    const deletedRecords = await RecipeActions.destroy(criteria)
      .fetch();
    if (deletedRecords.length === 0) throw flaverr('E_NOT_FOUND', new Error('No RecipeActions records found to delete'));
    return deletedRecords;
  },

  /**
   * Find records from the ChildrenRecipes table.
   * @param {Object} criteria - The criteria for finding ChildrenRecipes records.
   * @returns {Promise<Array>} The list of matching ChildrenRecipes records.
   */
  async find_children_recipes(criteria = {}) {
    try {
      return await ChildrenRecipes.find(criteria);
    } catch (err) {
      sails.log.error(`Error in ChildrenRecipes find: ${err.message}`);
      throw err;
    }
  },

  /**
   * Create a new record in the ChildrenRecipes table.
   * @param {Object} data - The data for the new ChildrenRecipes record.
   * @returns {Promise<Object>} The created ChildrenRecipes record.
   */
  async create_children_recipes(data) {
    try {
      return await ChildrenRecipes.create(data)
        .fetch();
    } catch (err) {
      sails.log.error(`Error in ChildrenRecipes create: ${err.message}`);
      throw err;
    }
  },

  /**
   * Update records in the ChildrenRecipes table.
   * @param {Object} criteria - The criteria to find the ChildrenRecipes records to update.
   * @param {Object} data - The data to update.
   * @returns {Promise<Array>} The updated ChildrenRecipes records.
   */
  async update_children_recipes(criteria, data) {
    try {
      const updatedRecords = await ChildrenRecipes.update(criteria)
        .set(data)
        .fetch();
      return updatedRecords;
    } catch (err) {
      sails.log.error(`Error in ChildrenRecipes update: ${err.message}`);
      throw err;
    }
  },

  /**
   * Delete records from the ChildrenRecipes table.
   * @param {Object} criteria - The criteria to find the ChildrenRecipes records to delete.
   * @returns {Promise<Array>} The deleted ChildrenRecipes records.
   */
  async delete_children_recipes(criteria) {
    const deletedRecords = await ChildrenRecipes.destroy(criteria)
      .fetch();
    if (deletedRecords.length === 0) throw flaverr('E_NOT_FOUND', new Error('No ChildrenRecipes records found to delete'));
    return deletedRecords;
  },

  /**
   * @description Updates deployment flags for recipe and schedules inside a DB transaction
   * @param {Object} payload - Feedback payload from IoT
   * @param {number} is_deployed - the deployment flag
   * @param {any} id
   * @returns {Promise<Object>}
   */
  updateDeploymentStatus: async (payload, id, is_deployed = 1) => {
    const {
      recipeInfo,
      scheduleInfo,
      siteId
    } = payload;
    const rid = recipeInfo?.rid;

    if (!siteId || !rid) {
      throw new Error('Missing siteId or recipe rid for deployment feedback update.');
    }

    return await sails.getDatastore('postgres')
      .transaction(async (db) => {
        await RecipeInfo.update({
          id,
          site_id: siteId
        })
          .set({ is_deployed })
          .usingConnection(db);

        if (Array.isArray(scheduleInfo) && scheduleInfo.length) {
          const scheduleIds = scheduleInfo.map((s) => Number(s.id));

          await RecipeSchedule.update({ id: { in: scheduleIds } })
            .set({ is_deployed })
            .usingConnection(db);
        }

        return { message: 'Deployment status for recipe and associated schedules has been updated.' };
      });
  }
};

async function _getRecipeTemplateUsedCount(templateId) {
  const query = 'select count(*) from recipe_info where recipe_info.recipe_template_id = $1';
  const result = await sails.getDatastore('postgres')
    .sendNativeQuery(query, [templateId]);
  return result?.rows?.[0]?.count ?? 0;
}

/**
 * @description Adds customDays to schedules with repeat_type 'custom' based on their cron expressions.
 * @param {Array} schedules - Array of schedule objects.
 * @returns {Array} - Updated schedules array.
 */
function _processSchedules(schedules) {
  if (_.isEmpty(schedules)) {
    return schedules;
  }

  return schedules.map(schedule => {
    if (schedule.repeat_type === 'custom') {
      return {
        ...schedule,
        customDays: extractDaysOfWeekFromCron(schedule.cron),
      };
    }
    return schedule;
  });
}

async function _updateSuperRecipe(data) {
  const {
    id,
    children_recipes,
    recipe_type,
    ...mainRecipeFields
  } = data;

  await updateMainRecipe(id, mainRecipeFields);
  await syncChildrenRecipes(id, children_recipes, recipe_type);
}

/**function to update main super recipe metadata*/
async function updateMainRecipe(id, fields) {
  const {
    title,
    description,
    recipe_category,
    run_interval,
    notify,
    smslist,
    components_type,
    priority,
    run_on,
    dependentOnOthers,
    controllers,
    last_updated_by,
    template_title,
  } = fields;

  await RecipeInfo.updateOne({ id })
    .set({
      title,
      description,
      recipe_category,
      run_interval,
      notify,
      smslist,
      components_type,
      priority,
      run_on,
      dependentOnOthers,
      controllers,
      last_updated_by,
      template_title,
    });
}

/**function to sync children recipes*/
async function syncChildrenRecipes(parentId, children_recipes, recipe_type) {
  const existingChildren = await ChildrenRecipes.find({
    parent_recipe_id: parentId,
    status: 1,
  });

  const existingChildIds = existingChildren.map(child => child.id);
  const incomingChildIds = children_recipes.filter(c => c.id)
    .map(c => c.id);

  const toDeactivateChildren = existingChildIds.filter(
    id => !incomingChildIds.includes(id)
  );

  if (!_.isEmpty(toDeactivateChildren)) {
    await ChildrenRecipes.update({ id: toDeactivateChildren })
      .set({ status: 0 });
  }

  for (const child of children_recipes) {
    const childId = await upsertChildRecipe(child, parentId);

    if (recipe_type === 'action') {
      await syncChildActions(childId, child.actions);
    }
  }
}

/**function to update or create a child recipe*/
async function upsertChildRecipe(child, parent_recipe_id) {
  const {
    id,
    execution_order,
    block_type,
    observation_time,
    expression_template,
    params,
    operators,
    everyMinuteTopics,
    description,
    formula,
    uniqid
  } = child;

  if (id) {
    await ChildrenRecipes.updateOne({ id })
      .set({
        execution_order,
        block_type,
        observation_time,
        expression_template,
        params,
        operators,
        everyMinuteTopics,
        description,
        formula,
      });
    return id;
  } else {
    const newChild = await ChildrenRecipes.create({
      execution_order,
      block_type,
      observation_time,
      expression_template,
      params,
      operators,
      everyMinuteTopics,
      description,
      uniqid,
      parent_recipe_id,
      formula,
    })
      .fetch();
    return newChild.id;
  }
}

/**function to sync actions under a child recipe*/
async function syncChildActions(recipe_id, actions = []) {
  const existingActions = await RecipeActions.find({
    recipe_id,
    status: 1
  });

  if (!_.isEmpty(existingActions)) {
    const existingActionIds = existingActions.map(a => a.id);
    const incomingActionIds = actions.filter(a => a.id)
      .map(a => a.id);

    const toDeactivate = existingActionIds.filter(
      id => !incomingActionIds.includes(id)
    );

    if (!_.isEmpty(toDeactivate)) {
      await RecipeActions.update({ id: toDeactivate })
        .set({ status: 0 });
    }
  }

  for (const action of actions) {
    const {
      id,
      execution_order,
      parent,
      command,
      did,
      value,
      uniqid,
    } = action;

    if (id) {
      await RecipeActions.updateOne({ id })
        .set({
          execution_order,
          parent,
          command,
          did,
          value,
        });
    } else {
      await RecipeActions.create({
        execution_order,
        parent,
        command,
        did,
        value,
        uniqid,
        recipe_id,
      });
    }
  }
}


