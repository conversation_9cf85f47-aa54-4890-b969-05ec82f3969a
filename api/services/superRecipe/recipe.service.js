const { v4: uuidv4 } = require("uuid");
const flaverr = require("flaverr");
const recipeService = require("./recipe.private");
const { normalizeFormula } = require("../../utils/super_recipe/utils");
const FormulaParser = require("./lib/recipe.observation.parser");
const SmartAlertRecipeSyncService = require("./lib/SmartAlertRecipeSyncService");

module.exports = {
  create: recipeService.create,
  createRecipe: async function (recipeData) {
    return await sails.getDatastore("postgres").transaction(async (connection) => {
      const { children_recipes = [], ...recipeInfo } = recipeData;

      const newRecipe = await RecipeInfo.create(recipeInfo).usingConnection(connection).fetch();

      for (const child of children_recipes) {
        const { actions = [], ...childData } = child;

        const newChildRecipe = await ChildrenRecipes.create({
          ...childData,
          parent_recipe_id: newRecipe.id,
        })
          .usingConnection(connection)
          .fetch();

        if (actions.length > 0) {
          const childActionsToInsert = actions.map((action) => ({
            ...action,
            recipe_id: newChildRecipe.id,
          }));

          await RecipeActions.createEach(childActionsToInsert).usingConnection(connection).fetch();
        }
      }

      return newRecipe;
    });
  },
  createRecipePayload: async function (recipeData, userId) {
    const { children_recipes = [], ...recipeInfo } = recipeData;

    const dependentOnOthers = [];
    let controllers = [];

    for (const children_recipe of children_recipes) {
      const { params, expression_template, operators, observation_time, block_type } = children_recipe;

      const normalizedExpression = block_type !== "else" ? normalizeFormula(expression_template) : "True";
      const formulaParser = new FormulaParser();

      let didArray = [];
      let everyMinuteTopics = [];
      let formula = "True";

      if (block_type !== "else") {
        const {
          didArray: parsedDidArray,
          everyMinuteTopics: parsedEveryMinuteTopics,
          formula: parsedFormula,
          runOnServer,
        } = await formulaParser.parseFormula(
          normalizedExpression,
          params,
          operators,
          observation_time
        );

        didArray = parsedDidArray;
        everyMinuteTopics = parsedEveryMinuteTopics;
        formula = parsedFormula;
      }

      children_recipe.everyMinuteTopics = everyMinuteTopics;
      children_recipe.formula = formula;
      children_recipe.uniqid = uuidv4();

      dependentOnOthers.push(didArray);

      const commandDidArr = children_recipe?.actions?.map((action) => action.did) || [];
      if (!_.isEmpty(children_recipe?.actions)) {
        for (const action of children_recipe?.actions) {
          action.uniqid = uuidv4();
        }
      }
      dependentOnOthers.push(commandDidArr);

      const controllerDetails = await formulaParser.getRunOnControllerId(dependentOnOthers, params);

      recipeInfo.run_on = controllerDetails.runOn;
      controllers = [...new Set([...controllers, ...controllerDetails.controllers])];
    }

    recipeInfo.dependentOnOthers = [...new Set(dependentOnOthers.flat())];
    recipeInfo.controllers = controllers;
    recipeInfo.switch_off = 0;
    recipeInfo.created_by = userId;
    recipeInfo.last_updated_by = userId;
    recipeInfo.rid = uuidv4();
    recipeInfo.template_title = recipeData?.template_title || recipeData?.title;

    return {
      ...recipeInfo,
      children_recipes,
    };

    async function manageRunningState(recipeId, status, userId, siteId) {
      const recipeDetail = await recipeService.findOne({
        id: recipeId,
        status: 1,
      });
      if (!recipeDetail) {
        throw flaverr("E_NOT_FOUND", new Error("Recipe not exists"));
      }

      if (!recipeDetail.is_deployed) {
        throw flaverr("E_NOT_DEPLOYED", new Error("Recipe not deployed"));
      }
    }
  },
  create_children_recipes: recipeService.create_children_recipes,
  create_recipe_actions: recipeService.create_recipe_actions,
  delete: recipeService.delete,
  deleteTemplateLinks: async function (recipeId) {
    const query = "update recipe_info ri set recipe_template_id = null where ri.recipe_template_id = $1";
    return await sails.getDatastore("postgres").sendNativeQuery(query, [recipeId]);
  },
  delete_children_recipes: recipeService.delete_children_recipes,
  delete_recipe_actions: recipeService.delete_recipe_actions,
  fetchAllSuperRecipeTemplatesBySiteId: recipeService.fetchAllSuperRecipeTemplatesBySiteId,
  fetchAllSuperRecipesBySiteId: recipeService.fetchAllSuperRecipesBySiteId,
  fetchSuperActionRecipeDetailById: async (siteId, id) => {
    const recipeDetail = await recipeService.fetchSuperRecipesById({ siteId, recipeType: "action", id });
    if (!recipeDetail) return null;
    const fetchActionDetailQuery = `select cr.id as children_recipe_id, ri.id as action_id, cr.execution_order as children_recipe_exec_order,
                                ra.command, ra.did, ra.parent, ra.value, ra.uniqid,
                                ra.execution_order as action_exec_order
                                from recipe_info ri
                                left join children_recipes cr on cr.parent_recipe_id = ri.id
                                left join recipe_actions ra on ra.recipe_id =cr.id
                                where ri.id = $1 and ri.status = 1 and cr.status=1 order by cr.execution_order asc, ra.execution_order`;
    const childrenRecipeActionDetail = await sails.getDatastore('postgres').sendNativeQuery(fetchActionDetailQuery, [recipeDetail.id]);
    const childrenRecipeActionMap = childrenRecipeActionDetail.rows.reduce((acm, curr) => {
      if (!acm.hasOwnProperty(curr.children_recipe_id)) acm[curr.children_recipe_id] = [];
      acm[curr.children_recipe_id].push(curr) //can delete extra keys from here
      return acm;
    }, {})

    recipeDetail.children_recipes.forEach((child) => {
      // eslint-disable-next-line no-param-reassign
      child.actions = childrenRecipeActionMap[child.id]
    })
    return recipeDetail;
  },
  fetchSuperAlertRecipeDetailById: async (siteId, id) => {
    const alertRecipeDetail = await recipeService.fetchSuperRecipesById({ siteId, recipeType: "alert", id });
    if (!alertRecipeDetail) return null;

    const { rid } = alertRecipeDetail;
    delete alertRecipeDetail.template_title;
    delete alertRecipeDetail.is_recipe_template;

    alertRecipeDetail.smartAlertId = null;
    alertRecipeDetail.escalation_time_in_min = null;
    alertRecipeDetail.escalated_to = [];
    alertRecipeDetail.subscribers = [];

    const smartAlertDetailQuery = `select at2.observer_execution_ref_id as "recipeId",at2.id as "smartAlertId", at2.severity,
      at2.escalation_time_in_min,
      at2.escalated_to, as2.id,
      as2.user_id as subscriber_id,
      as2.notify_on_sms,
      as2.notify_on_whatsapp,
      as2.notify_on_email
    FROM alert_template at2
    left join alert_subscribers as2 on as2.alert_template_ref_id = at2.id and as2.status=1
    where at2.observer_execution_ref_id=$1`;

    const smartAlertDetailResult = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).sendNativeQuery(smartAlertDetailQuery, [rid]);
    const smartAlertDetailResultMap = smartAlertDetailResult.rows.reduce((acm, curr) => {
      if (!acm.hasOwnProperty(curr.recipeId)) {
        acm[curr.recipeId] = curr
        acm[curr.recipeId].subscribers = [];
      }
      acm[curr.recipeId].subscribers.push({
        subscriberId: curr.subscriber_id,
        smsSubscribed: curr.notify_on_sms,
        whatsappSubscribed: curr.notify_on_whatsapp,
        emailSubscribed: curr.notify_on_email,
      })
      delete acm[curr.recipeId].subscriber_id;
      delete acm[curr.recipeId].notify_on_sms;
      delete acm[curr.recipeId].notify_on_whatsapp;
      delete acm[curr.recipeId].notify_on_email;
      return acm;
    }, {});
    if (_.isEmpty(smartAlertDetailResult)) return alertRecipeDetail;
    return Object.assign(alertRecipeDetail, smartAlertDetailResultMap[rid]);
  },
  fetchSuperAlertRecipeListBySiteId: async (siteId) => {
    const recipeList = await recipeService.fetchAllSuperRecipesBySiteId(siteId, "alert");
    if (recipeList.length === 0) return [];
    const alertRecipeDetailMap = recipeList.reduce((acm, curr) => {
      const { rid } = curr;
      delete curr.children_recipes;
      delete curr.template_title;
      delete curr.is_recipe_template;

      // eslint-disable-next-line no-param-reassign
      acm[rid] = curr;
      acm[rid].smartAlertId = null;
      acm[rid].escalation_time_in_min = null;
      acm[rid].escalated_to = [];
      acm[rid].subscribers = [];
      return acm;
    }, {});

    const recipeIds = recipeList.map((recipe) => recipe.rid);
    const smartAlertDetailQuery = `select at2.observer_execution_ref_id as "recipeId",at2.id as "smartAlertId", at2.severity,
      at2.escalation_time_in_min,
      at2.escalated_to, as2.id,
      as2.user_id as subscriber_id,
      as2.notify_on_sms,
      as2.notify_on_whatsapp,
      as2.notify_on_email
    FROM alert_template at2
    left join alert_subscribers as2 on as2.alert_template_ref_id = at2.id and as2.status=1
    where at2.observer_execution_ref_id IN (${recipeIds.map((id) => `'${id}'`).join(",")})`;

    const smartAlertDetailResult = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).sendNativeQuery(smartAlertDetailQuery);
    const smartAlertDetailResultMap = smartAlertDetailResult.rows.reduce((acm, curr) => {
      if (!acm.hasOwnProperty(curr.recipeId)) {
        acm[curr.recipeId] = curr
        acm[curr.recipeId].subscribers = [];
      }
      acm[curr.recipeId].subscribers.push({
        subscriberId: curr.subscriber_id,
        smsSubscribed: curr.notify_on_sms,
        whatsappSubscribed: curr.notify_on_whatsapp,
        emailSubscribed: curr.notify_on_email,
      })
      delete acm[curr.recipeId].subscriber_id;
      delete acm[curr.recipeId].notify_on_sms;
      delete acm[curr.recipeId].notify_on_whatsapp;
      delete acm[curr.recipeId].notify_on_email;
      return acm;
    }, {});

    for (const rid in alertRecipeDetailMap) {
      if (!smartAlertDetailResultMap.hasOwnProperty(rid)) continue;
      alertRecipeDetailMap[rid].smartAlertId = smartAlertDetailResultMap[rid].smartAlertId;
      alertRecipeDetailMap[rid].escalation_time_in_min = smartAlertDetailResultMap[rid].escalation_time_in_min || null;
      alertRecipeDetailMap[rid].escalated_to = smartAlertDetailResultMap[rid].escalation_time_in_min || [];
      alertRecipeDetailMap[rid].subscribers = smartAlertDetailResultMap[rid].subscribers || [];
    }
    return Object.values(alertRecipeDetailMap);
  },
  fetchSuperActionRecipeListBySiteId: async (siteId) => {
    const actionRecipeList = await recipeService.fetchAllSuperRecipesBySiteId(siteId, "action");
    return actionRecipeList.map((reciped) => {
      delete reciped.children_recipes;
      delete reciped.template_title;
      delete reciped.is_recipe_template;
      return reciped
    })
  },
  find: recipeService.find,
  findOne: recipeService.findOne,
  find_children_recipes: recipeService.find_children_recipes,
  find_recipe_actions: recipeService.find_recipe_actions,
  getActionRecipeInfoById: recipeService.getActionRecipeInfoById,
  getActionRecipeTemplateInfoById: recipeService.getActionRecipeTemplateInfoById,
  getRecipeTemplateUsedCount: recipeService.getRecipeTemplateUsedCount,
  update: recipeService.update,
  updateDeploymentStatus: recipeService.updateDeploymentStatus,
  update_children_recipes: recipeService.update_children_recipes,
  update_recipe_actions: recipeService.update_recipe_actions,
  syncSmartAlertRecipeById: async function (siteId, id) {
    const alertRecipeSyncInstance = await SmartAlertRecipeSyncService.build(siteId, id)
    return alertRecipeSyncInstance.sync();
  }

};
