const moment = require('moment-timezone');

class RecipeIotPayloadBuilder {
  constructor(recipeInfo) {
    this.recipeInfo = recipeInfo;
  }

  buildActionAlert(actions, uniqId, category, priority) {
    return actions.sort((a, b) => a.execution_order - b.execution_order)
      .map(action => {
        const basePayload = {
          title: this.recipeInfo?.title,
          description: this.recipeInfo.description,
          type: this.recipeInfo?.recipe_type,
          notify: this.recipeInfo.notify || [],
          smslist: this.recipeInfo.smslist || [],
          accountable: this.recipeInfo.notify || [],
          priority,
          uniqId: action.uniqid,
          category,
        };

        if (this.recipeInfo?.recipe_type === 'action') {
          return {
            ...basePayload,
            controls_rel: {},
            command: action.command,
            did: action.did,
            parent: action.parent,
            value: action.value,
          };
        }

        return basePayload;
      });
  }

  buildChildrenRecipes() {
    const children = {};
    this.recipeInfo.children_recipes.sort((a, b) => a.execution_order - b.execution_order)
      .forEach(recipe => {
        const blockId = recipe.block_type !== 'else' ? `if/${recipe.execution_order}` : recipe.block_type;
        children[blockId] = {
          everyMinuteTopics: recipe.everyMinuteTopics || [],
          topics: recipe.everyMinuteTopics || [],
          recipe: recipe.formula,
          syncAsyncAction: 'True',
          startNow: 'True',
          maxDataNeeded: recipe.observation_time.toString(),
          rid: `${this.recipeInfo.rid}`,
          notRun: '[]',
          blockId: recipe?.uniqid,
          appType: this.recipeInfo.app_type,
          actionAlert: this.buildActionAlert(
            recipe.actions,
            recipe.uniqid + '/' + blockId,
            [this.recipeInfo.recipe_category],
            this.recipeInfo.priority
          ),
        };
      });
    return children;
  }

  buildPayload() {
    return {
      operation: 'recipeInit',
      scheduleInfo: this.recipeInfo.schedules.map(schedule => ({
        rid: this.recipeInfo.rid,
        id: schedule.id || '',
        cron: schedule.cron || '* * * * *',
        startdate: moment(schedule.start_date)
          .format('YYYY-MM-DD HH:mm') || '',
        enddate: moment(schedule.end_date)
          .format('YYYY-MM-DD HH:mm') || '',
      })),
      recipeInfo: {
        // runOn: this.recipeInfo.run_on,
        runOn: 8772,
        failsafe: '{}',
        dependentOnOthers: [...new Set(this.recipeInfo.dependentOnOthers.map(String))],
        controllers: [...new Set(this.recipeInfo.controllers.map(String))],
        startNow: 'True',
        maxDataNeeded: this.recipeInfo.run_interval.toString(),
        rid: this.recipeInfo.rid,
        notRun: '[]',
        appType: this.recipeInfo.app_type,
        actionAlert: [
          {
            category: [this.recipeInfo.recipe_category],
            parent: this.recipeInfo?.children_recipes?.[0]?.actions?.[0]?.parent,
            title: this.recipeInfo.title,
            description: this.recipeInfo.description,
            priority: this.recipeInfo.priority,
            switchOff: this.recipeInfo.switch_off.toString(),
            type: this.recipeInfo.app_type,
            childrenRecipe: this.buildChildrenRecipes(),
          },
        ],
      },
    };
  }
}

module.exports = RecipeIotPayloadBuilder;


