const iotCoreService = require('../../iotCore/iotCore.public');
const flaverr = require('flaverr');

/**Topic map for IoT operations*/
const iotTopicMap = {
  'RUNNING_RECIPE_CONTROL': '{{siteId}}/config/{{runOn}}/recipecontrol',
  'RECIPE_MANAGEMENT': '{{siteId}}/config/{{runOn}}/recipelogic',
};

/**
 * @description Topic Factory for dynamically generating topics.
 */
class TopicFactory {
  /**
   * Generates a topic string by replacing placeholders with actual values.
   * @param {string} topicTemplate - The topic template string with placeholders.
   * @param {object} replacements - Object containing placeholder keys and their values.
   * @returns {string} - The resolved topic string.
   */
  static createTopic(topicTemplate, replacements) {
    return topicTemplate.replace(/{{(\w+)}}/g, (_, key) => replacements[key] || '');
  }
}

class RecipeIotCommunicationInterface {
  static async updateSuperRecipeFromController(siteId, runOn, updatedRecipeObject) {
    const topicTemplate = iotTopicMap['RECIPE_MANAGEMENT'];
    const topic = TopicFactory.createTopic(topicTemplate, {
      siteId,
      runOn
    });

    const payload = {
      data: updatedRecipeObject,
      func: 'updateRecipe',
      operation: 'recipeControl',
    };

    return await iotCoreService.publish(topic, payload);
  }

  static async deploySuperRecipe(siteId, runOn, recipeObject) {
    const topicTemplate = iotTopicMap['RECIPE_MANAGEMENT'];
    const topic = TopicFactory.createTopic(topicTemplate, {
      siteId,
      runOn
    });

    return await iotCoreService.publish(topic, recipeObject);
  }

  static async deleteSuperRecipeFromController(siteId, runOn, recipeId) {
    const topicTemplate = iotTopicMap['RECIPE_MANAGEMENT'];
    const topic = TopicFactory.createTopic(topicTemplate, {
      siteId,
      runOn
    });

    const payload = {
      data: { rid: recipeId },
      func: 'deleteRecipe',
      operation: 'recipeControl',
    };

    return await iotCoreService.publish(topic, payload);
  }

  static async deleteSuperRecipeScheduleFromController(siteId, runOn, recipeId, scheduleId) {
    const topicTemplate = iotTopicMap['RECIPE_MANAGEMENT'];
    const topic = TopicFactory.createTopic(topicTemplate, {
      siteId,
      runOn
    });

    const payload = {
      data: {
        rid: recipeId,
        sid: scheduleId
      },
      func: 'deleteSchedule',
      operation: 'recipeControl',
    };

    return await iotCoreService.publish(topic, payload);
  }

  /**status = 1 means stop the recipe, 0 means start the recipe.*/
  static async updateRunningState(siteId, runOn, status, recipeId) {
    if (![0, 1].includes(+status)) {
      throw flaverr('E_IOT_COMMUNICATION_ERROR', new Error('Invalid status value must be 0 or 1 only'));
    }

    const topicTemplate = iotTopicMap['RUNNING_RECIPE_CONTROL'];
    const topic = TopicFactory.createTopic(topicTemplate, {
      siteId,
      runOn
    });

    const payload = {
      func: 'startStopRecipe',
      operation: 'recipeControl',
      data: [{
        rid: recipeId,
        switchOff: String(status),
      }]
    };

    return await iotCoreService.publish(topic, payload);
  }
}

module.exports = RecipeIotCommunicationInterface;
