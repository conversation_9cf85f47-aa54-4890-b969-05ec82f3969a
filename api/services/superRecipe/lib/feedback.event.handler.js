const socketService = require('../../socket/socket.public');
const recipeService = require('../recipe.public');
const scheduleService = require('../schedule/schedule.public');
const {
  syncRecipeInSmartAlert,
  deleteRecipeFromSmartAlert
} = require('../../smartAlert/smartAlert.public');

/**
 * @description Handles socket emission and optional DB update for recipe feedback based on payload.
 * @param {Object} payload - The full feedback payload
 */
async function handleRecipeFeedbackEvent(payload) {
  const {
    siteId,
    operation,
    recipeInfo,
    func
  } = payload;

  const rid = payload?.recipeInfo?.rid || payload?.data?.[0]?.rid || payload?.data?.rid;

  if (!rid || !siteId) {
    sails.log.error('[SuperRecipe]->[handleRecipeFeedbackEvent] Missing required siteId or rid.');
    return null;
  }

  const recipeDetails = await recipeService.findOne({
    rid,
    site_id: siteId
  });

  if (!recipeDetails) {
    if (payload?.data?.appType === 'recipe') {
      return _handleLegacyRecipeFeedback(siteId, operation, func, payload, rid);
    }
    if (func === 'deleteRecipe') {
      sails.log.warn(`[SuperRecipe]->[handleRecipeFeedbackEvent] No recipe found for rid=${rid}, falling back to legacy handler for delete.`);
      return _handleLegacyRecipeFeedback(siteId, operation, func, payload, rid);
    }
    sails.log.error(`[SuperRecipe]->[handleRecipeFeedbackEvent] No recipe found with rid=${rid} for siteId=${siteId}`);
    return null;
  }

  const { id } = recipeDetails;

  if (operation === 'recipeInit') {
    return _handleRecipeInitFeedback(siteId, rid, payload, id);
  }

  if (func === 'startStopRecipe') {
    return _handleStartStopFeedback(payload, id);
  }

  if (func === 'deleteRecipe') {
    return _handleDeleteRecipeFeedback(siteId, rid, recipeInfo || {}, id);
  }

  if (func === 'updateRecipe') {
    return _handleUpdateRecipeFeedback(siteId, rid, payload, id);
  }

  return null;
}

async function _handleRecipeInitFeedback(siteId, rid, payload, id) {
  await recipeService.updateDeploymentStatus(payload, id, 1);
  await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
    data: {
      recipe: rid,
      recipeId: id
    },
    message: `Recipe with id ${id} deployed successfully`,
    event: 'deployRecipe',
  });
  sails.log.info(`Recipe with id ${id} deployed successfully`);
  return { message: `Recipe with id ${id} deployed successfully` };
}

/**
 * @description Handles feedback for start/stop recipe event.
 * @param {Object} payload - Feedback payload from IOT side.
 * @param {number} id - Recipe ID from DB
 */
async function _handleStartStopFeedback(payload, id) {
  const {
    siteId,
    data = []
  } = payload;

  if (_.isEmpty(data)) {
    sails.log.error('[SuperRecipe]->[handleRecipeFeedbackEvent] Incomplete data object from IOT');
    return { message: 'No data received for start/stop feedback.' };
  }

  const {
    rid,
    switchOff
  } = data[0];
  const isOff = switchOff === '1' || switchOff === 1;

  await RecipeInfo.updateOne({
    id,
    site_id: siteId
  })
    .set({ switch_off: isOff ? 1 : 0 });

  await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
    data: {
      recipe: rid,
      switchOff: isOff,
    },
    message: 'Switch status updated and start/stop event emitted successfully.',
    event: 'startStopRecipe',
  });

  return { message: 'Switch status updated and start/stop event emitted successfully.' };
}

/**
 * @description Handles feedback for recipe deletion.
 * @param {string} siteId
 * @param {string} rid
 * @param {Object} data
 * @param {number} id
 */
async function _handleDeleteRecipeFeedback(siteId, rid, data, id) {
  await Promise.all([
    recipeService.delete(id),
    recipeService.delete_children_recipes({ parent_recipe_id: id }),
    scheduleService.delete({ recipe_id: id })
  ]);

  await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
    data: {
      ...data,
      recipe: rid,
      recipeId: id,
    },
    message: `Recipe with id ${id} deleted successfully.`,
    event: 'deleteRecipe',
  });

  return { message: `Recipe with id ${id} deleted successfully.` };
}

async function _handleUpdateRecipeFeedback(siteId, rid, payload, id) {
  await recipeService.updateDeploymentStatus(payload, id, 0);
  await socketService.notifyServiceOnJouleTrack(siteId, 'recipies', 'recipe', {
    data: {
      recipe: rid,
      recipeId: id
    },
    message: `Recipe with id ${id} updated successfully`,
    event: 'updateRecipe',
  });
  sails.log.info(`Recipe with id ${id} updated successfully`);
  return { message: `Recipe with id ${id} updated successfully` };
}

async function _handleLegacyRecipeFeedback(siteId, operation, func, payload, rid) {
  try {
    if (
      func === 'updateRecipe' &&
      payload?.data?.actionable?.[0]?.type === 'alert'
    ) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await syncRecipeInSmartAlert(rid);
    }

    if (func === 'deleteRecipe') {
      try {
        await deleteRecipeFromSmartAlert(rid);
      } catch (e) {
        sails.log.error('[Delete-Recipe-From-Smart-Alert]');
        sails.log.error(e);
      }
    }
  } catch (err) {
    sails.log.error(`[legacyRecipe -> handle-recipe-feedback -> ${func}]`, err);
    return null;
  }
}

module.exports = {
  handleRecipeFeedbackEvent,
};
