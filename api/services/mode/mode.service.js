/* eslint-disable no-shadow */
const moment = require("moment-timezone");
const modeService = require("./mode.private");
const deviceService = require("../device/device.public");
const utils = require("../../utils/mode/utils");
const rabbitMQService = require("../rabbitmq/rabbitmq.public");
const modeFeedbackQueue = "MODE_FEEDBACK_QUEUE";
const cacheService = require("../cache/cache.service");
const iotCoreService = require("../iotCore/iotCore.public");

moment.tz.add("Asia/Kolkata|HMT +0630 IST|-5R.k -6u -5u|01212|-18LFR.k 1unn.k HB0 7zX0|15e6");
moment.tz.setDefault("Asia/Kolkata");

const DEFAULT_MODE = "jouletrack";
module.exports = {
  create: modeService.create,
  find: modeService.find,
  findOne: modeService.findOne,
  update: modeService.update,
  delete: modeService.delete,
  MODE_FEEDBACK_QUEUE: function rabbitmqInstance() {
    // eslint-disable-next-line new-cap
    return new rabbitMQService.consumerWithAckQueue(modeFeedbackQueue);
  },

  /**
   * Get the last known mode of device's parameter. By default the modes are jouletrack.
   * @param {string} deviceId Unique id of device
   * @return {string} the last known mode or jouletrack
   possible modes = enum(jouletrack, joulerecipe)
   */
  getLastKnownModeOfDeviceParam: async function (deviceId, parameter) {
    let currentTimeStampInUnix = globalHelpers.getCurrentUnixTs();
    let currentMode;
    let deviceIdDotParam = `${deviceId}.${parameter}`;
    let mode;

    try {
      mode = await modeService.findOne({
        did: deviceIdDotParam,
        timestamp: {
          lte: currentTimeStampInUnix,
        },
      });
    } catch (e) {
      throw e;
    }

    currentMode = utils.getDeviceParameterModeFromModePacket(mode, parameter);
    return currentMode;
  },

  /**
   * Get modes of device's parameter between the two timestamps.
   * @param {string} did - Unique id of device and its param @example 3028.setfrequency.
   * @param startTime - start time in unix.
   * @param endTime - end time in unix.
   * @return {Array} of modes.
   */
  getModesBetweenTwoTimeStamp: async function (did, startTime, endTime) {
    let modes;
    // eslint-disable-next-line no-useless-catch
    try {
      if (startTime > endTime) {
        return { problems: ["start cannot be greater than endtime"] };
      }
      modes = await modeService.find({
        did,
        timestamp: { in: [startTime, endTime] },
      });
    } catch (e) {
      throw e;
    }
    return modes;
  },

  /**
   * Get last mode of device's parameter.
   * @param {string} did - Unique id of device and its param @example 3028.setfrequency.
   * @param startTime - start time in unix.
   * @return {object} of mode.
   */
  getLastModeOfDeviceParam: async function (did, startTime) {
    let modes;
    // eslint-disable-next-line no-useless-catch
    try {
      modes = await modeService.find({
        did,
        timestamp: { "<=": startTime },
      });
    } catch (e) {
      throw e;
    }

    return modes[modes.length - 1];
  },
  modeFeedbackMessageHandler: async function (message) {
    /*mode Feedback message implementation function*/
    console.log(message);
  },

  /**
   * @description Fetches the configured parameters based on the
   * provided system ID and device Id also checks relationship exists or not.
   * @param {string} deviceId - The ID of the component
   * @param {string} systemId - The ID of the system.
   * @returns {Promise<Array>} The configured parameters.
   * @throws {Error} If an error occurs while fetching the configured parameters.
   */
  fetchControlsConfigurator: async function (deviceId, systemId) {
    const query = `select dccr.control_property, dccr.control_name,dccr.control_abbr,dccr.control_type  from configurator_tagged_devices_param ctdp
    join configurator_tagged_devices ctd on ctd.id=ctdp.configurator_tagged_devices_id
    join device_control_config_relationship dccr on dccr.device_id = ctd.device_id and dccr.control_abbr =ctdp.param_abbr
    where ctdp.param_type ='controls' and ctd.device_id = $1
    and ctd.configurator_system_id = $2 and ctd.status =1 and ctdp.status=1 and dccr.status=1
    order by ctdp."order" ASC`;
    let controlsParamConfigurator = await sails
      .getDatastore("postgres")
      .sendNativeQuery(query, [deviceId, systemId]);
    const { rows } = controlsParamConfigurator;
    // eslint-disable-next-line no-return-await
    return await Promise.all(rows);
  },

  /**
   * @description Fetches mode component details based on the provided parameters.
   * @param {Object} params - The parameters for fetching mode component details.
   * @param {string} params.deviceId - The ID of the device.
   * @param {string} params.key - The key for the mode.
   * @param {boolean} isConfigurator - Indicates whether it's for configurator or not.
   * @returns {Promise<Object|string>} The mode component details or mode string.
   * @throws {Error} If an error occurs while fetching the device or parsing the mode.
   */
  fetchComponentModeDetails: async function (params, isConfigurator) {
    let currentMode;
    // eslint-disable-next-line no-useless-catch
    try {
      currentMode = await deviceService.findOne(params.deviceId);
    } catch (err) {
      throw err;
    }

    const modeObj = JSON.parse(currentMode.mode);
    const paramMode = modeObj[params.key];
    const mode = utils.getModeWithLabel(paramMode);
    if (isConfigurator) {
      return mode;
    }
    const currentModeDataObj = {
      mode: mode,
      commandAbbr: params.key,
      label: params.displayName,
      deviceId: params.deviceId,
    };
    return currentModeDataObj;
  },

  /**
   * @description fetch the last known mode of a deviceId and control abbr
   * @param {number} deviceId - deviceId
   * @param {string} commandAbbr - start,stop, changesetpoint etc
   * @returns {Promise<Object|undefined>} last know mode detail with timestamp
   */
  fetchLastKnowModeByDeviceCommandAbbr: async (deviceId, commandAbbr) => {
    const did = `${deviceId}.${commandAbbr}`;
    // eslint-disable-next-line no-use-before-define
    // const cachedComponentModeRecord = await getModeFromCache(deviceId, commandAbbr)
    // if (!_.isEmpty(cachedComponentModeRecord)) {
    //   // eslint-disable-next-line no-shadow
    //   const { commandAbbr, deviceId, mode: currMode, timestamp } = cachedComponentModeRecord;
    //   // eslint-disable-next-line no-use-before-define
    //   return formatModeResponse({
    //     commandAbbr,
    //     deviceId,
    //     currMode,
    //     timestamp
    //   })
    // }

    let currMode = DEFAULT_MODE;
    let timestamp = new Date().getTime() * 1000;
    // eslint-disable-next-line no-undef
    const modeRecord = await Modes.find({
      did,
    })
      .sort("timestamp DESC")
      .limit(1);
    if (modeRecord.length) {
      currMode = modeRecord[0].currMode;
      timestamp = modeRecord[0].timestamp;
    }

    // eslint-disable-next-line no-use-before-define
    await setModeToCache({
      commandAbbr,
      deviceId,
      mode: currMode,
      timestamp,
    });
    // eslint-disable-next-line no-use-before-define
    return formatModeResponse({
      commandAbbr,
      deviceId,
      currMode,
      timestamp,
    });

    function formatModeResponse({
      // eslint-disable-next-line no-shadow
      commandAbbr,
      deviceId,
      currMode,
      timestamp,
    }) {
      const formattedComponentMode = {
        commandAbbr,
        deviceId,
        mode: {
          key: currMode,
          label: sails.config.custom.MODE_KEY_MAP[currMode] || currMode,
        },
        label: commandAbbr,
        timestamp: moment.unix(Number.parseInt(timestamp, 10) / 1000).toISOString(),
      };
      return formattedComponentMode;
    }
  },
  changeControlModeByCommand: async function (modeChangePayload) {
    const uniqueDeviceIdSet = new Set();
    modeChangePayload.forEach(({ deviceId }) => uniqueDeviceIdSet.add(deviceId));
    const deviceIds = Array.from(uniqueDeviceIdSet);
    const deviceControllerData = await Promise.all(
      deviceIds.map(deviceService.findControllerIdByDeviceId),
    );
    const deviceControllerMap = deviceControllerData.reduce((acm, curr) => {
      acm[curr.deviceId] = curr.controllerId;
      return acm;
    }, {});

    const controllerWiseModeChangePayload = {};
    for (const it of modeChangePayload) {
      const { commandAbbr, mode, deviceId, siteId } = it;
      const controllerId = deviceControllerMap[deviceId];
      if (!controllerWiseModeChangePayload.hasOwnProperty(controllerId)) {
        controllerWiseModeChangePayload[controllerId] = {
          operation: "InsertInModes",
          extra: { controllerId, siteId },
          config: {
            [`${deviceId}.${commandAbbr}`]: mode,
          },
        };
      } else {
        controllerWiseModeChangePayload[controllerId].config[`${deviceId}.${commandAbbr}`] = mode;
      }
    }
    await Promise.all(
      Object.values(controllerWiseModeChangePayload).map((payload) => {
        const {
          extra: { controllerId, siteId },
        } = payload;
        const TOPIC = `${siteId}/config/${controllerId}/mode`;
        iotCoreService.publish(TOPIC, payload);
      }),
    );
    return Object.values(controllerWiseModeChangePayload);
  },
  DEFAULT_MODE: DEFAULT_MODE,
};

const getModeFromCache = async (deviceId, commandAbbr) => {
  const cachedKey = `mode:deviceId:${deviceId}:commandAbbr:${commandAbbr}`;
  const cachedData = await cacheService.get(cachedKey);
  if (!cachedData) return;
  // eslint-disable-next-line consistent-return
  return JSON.parse(cachedData);
};

const setModeToCache = async ({ commandAbbr, deviceId, mode: currMode, timestamp }) => {
  const cachedKey = `mode:deviceId:${deviceId}:commandAbbr:${commandAbbr}`;
  await cacheService.set(
    cachedKey,
    JSON.stringify({
      commandAbbr,
      deviceId,
      mode: currMode,
      timestamp,
    }),
  );
  const EXPIRY = 15 * 60; // 15 Minutes
  await cacheService.expire(cachedKey, EXPIRY);
};
