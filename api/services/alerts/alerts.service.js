module.exports = {
  async getAlertCount(params) {
    const {
      siteId,
      startTime,
      endTime,
      userId
    } = params;

    const query = `
      SELECT COUNT(DISTINCT CASE
                              WHEN as2.subscriber_id = $2
                                THEN aih.alert_inventory_id END) AS alert_count_for_subscriber,
             COUNT(DISTINCT aih.alert_inventory_id)              AS total_alert_count
      FROM alert_incident_history aih
             JOIN alert_inventory ai ON ai.id = aih.alert_inventory_id
             LEFT JOIN alert_subscribers as2 ON as2.alert_id = ai.id
      WHERE issue_resolved_at IS NULL
        AND ai.siteid = $1
        AND ai.status = 1
    `;

    const queryParams = [siteId, userId];

    const result = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME)
      .sendNativeQuery(query, queryParams);

    return _.get(result, 'rows[0]', {});
    ;
  }
};
