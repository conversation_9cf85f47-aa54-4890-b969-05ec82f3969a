/**
 * alertIncidentHistory.service.js
 * @description :: Service layer with business logic for alert incident history
 */

const alertIncidentHistoryService = require('./alertIncidentHistory.private');
const moment = require('moment-timezone');
const flaverr = require('flaverr');
const RedisClient = require('../../cache/cache.public');

async function getBatchRecentOccurrencesFromRedis(alerts) {
  const statefulAlerts = alerts.filter(alert => alert.isStateful);
  if (statefulAlerts.length === 0) return null;

  try {
    const keys = statefulAlerts.flatMap(alert => [
      `alert_occurrence:${alert.siteId}:incident:${alert.incidentId}:count`,
      `alert_occurrence:${alert.siteId}:incident:${alert.incidentId}:ts`
    ]);

    const results = await RedisClient.mget(keys);

    if (!Array.isArray(results) || results.length !== keys.length) {
      sails.log.error('Redis mget returned invalid results', {
        expectedLength: keys.length,
        actualLength: results?.length
      });
      return null;
    }

    return statefulAlerts.reduce((acc, alert, index) => {
      const baseIndex = index * 2;
      const count = results[baseIndex];
      const recentTs = results[baseIndex + 1];

      acc[alert.incidentId] = {
        count: count ? parseInt(count) : null,
        recentTs: recentTs || null
      };
      return acc;
    }, {});
  } catch (error) {
    sails.log.error(`Error batch fetching from Redis: ${error.message}`);
    return null;
  }
}

module.exports = {
  /**
   * Acknowledge an alert incident
   * @param {string} userId - User ID acknowledging the alert
   * @param {string} siteId - Site ID where the alert belongs
   * @param {number} alertId - Alert inventory ID
   * @returns {Promise<Object>} Updated alert incident
   */
  acknowledge: async function (userId, siteId, alertId) {
    const alertInventory = await AlertInventory.findOne({
      id: alertId,
      siteId
    });

    if (!alertInventory) {
      throw flaverr({
        code: 'E_NOT_FOUND',
        message: `Alert with id ${alertId} not found for site ${siteId}`
      });
    }

    const subscription = await AlertSubscribers.findOne({
      alert_id: alertId,
      subscriber_id: userId,
      status: 1
    });

    if (!subscription) {
      throw flaverr({
        code: 'E_NOT_SUBSCRIBED',
        message: `User is not subscribed to alert ${alertId}`
      });
    }

    const activeIncident = await alertIncidentHistoryService.findOne({
      alert_inventory_id: alertId,
      issue_resolved_at: null
    });

    if (!activeIncident) {
      throw flaverr({
        code: 'E_NOT_FOUND',
        message: `No active incident found for alert ${alertId}`
      });
    }

    if (activeIncident.acknowledge_by || activeIncident.acknowledge_ts) {
      throw flaverr({
        code: 'E_ALREADY_ACKNOWLEDGED',
        message: `Alert incident is already acknowledged by ${activeIncident.acknowledge_by}`
      });
    }

    if (alertInventory.escalation_time_in_min) {
      const currentTime = moment().tz('UTC');
      const escalationThreshold = moment(activeIncident.issue_occurred_at).tz('UTC')
        .add(alertInventory.escalation_time_in_min, 'minutes');

      if (currentTime.isAfter(escalationThreshold)) {
        throw flaverr({
          code: 'E_ALREADY_ESCALATED',
          message: `Alert has already been escalated and cannot be acknowledged`
        });
      }
    }

    const acknowledgeTs = moment().tz('UTC').toDate();
    return await alertIncidentHistoryService.updateOne(
      { id: activeIncident.id },
      {
        acknowledge_by: userId,
        acknowledge_ts: acknowledgeTs
      }
    );
  },

  /**
   * List alerts with their details
   * @param {Object} params - Parameters for listing alerts
   * @returns {Promise<Array>} List of alerts
   */
  listAlerts: async function (params) {
    const {
      siteId,
      startTime,
      endTime,
      alertStatus = 'active',
      subscriberId,
      assetType,
      severity,
      durationFilter,
      search,
      sortBy = 'occurredAt',
      sortOrder = 'desc',
      assetId
    } = params;

    const queryParams = [startTime, endTime, siteId];
    let paramCount = 4;

    let query = `
      SELECT
        aih.id as incident_id,
        aih.issue_occurred_at,
        aih.issue_resolved_at,
        aih.acknowledge_by,
        aih.acknowledge_ts,
        aih.occurred_event_count,
        aih.recent_occurred_event_ts,
        CASE
          WHEN aih.issue_resolved_at IS NULL THEN 1
          ELSE 0
        END AS is_stateful,
        CASE
          WHEN aih.acknowledge_by IS NOT NULL AND aih.acknowledge_ts IS NOT NULL THEN 1
          ELSE 0
        END AS is_acknowledged,
        incident_summary.total_duration,
        incident_summary.duration_percentage,
        ai.id as alert_id,
        ai.name as alert_name,
        ai.description,
        ai.severity,
        ai.asset_id as device_id,
        ai.asset_type as device_type,
        ai.siteid as site_id,
        ai.escalation_time_in_min,
        ai.escalated_to,
        at.id as alert_group_id,
        at.alert_category as alert_type
      FROM
        alert_incident_history aih
      JOIN (
        SELECT
          aih.alert_inventory_id,
          ROUND(
            LEAST(
              SUM(EXTRACT(EPOCH FROM (
                LEAST(COALESCE(aih.issue_resolved_at, $2), $2) - GREATEST(aih.issue_occurred_at, $1)
              ))
            ) / 60,
              EXTRACT(EPOCH FROM ($2 - $1))/60
            )
          ) AS total_duration,
          ROUND(
            LEAST(
              (SUM(
                EXTRACT(EPOCH FROM (
                  LEAST(COALESCE(aih.issue_resolved_at, $2), $2) - GREATEST(aih.issue_occurred_at, $1)
                ))
              ) * 100.0) /
              EXTRACT(EPOCH FROM ($2 - $1)),
              100
            )
          ) AS duration_percentage,
          MAX(aih.id) AS latest_incident_id
        FROM
          alert_incident_history aih
        JOIN
          alert_inventory ai ON ai.id = aih.alert_inventory_id
        JOIN
          alert_template at ON at.id = ai.alert_template_ref_id
        WHERE
          ai.siteid = $3
          AND at.status = 1
          AND aih.issue_occurred_at <= $2
          AND (aih.issue_resolved_at IS NULL OR aih.issue_resolved_at >= $1)
        GROUP BY
          aih.alert_inventory_id
      ) incident_summary ON incident_summary.latest_incident_id = aih.id
      JOIN alert_inventory ai ON ai.id = aih.alert_inventory_id
      JOIN alert_template at ON at.id = ai.alert_template_ref_id
      WHERE
        ai.siteid = $3
    `;

    if (alertStatus !== 'all') {
      if (alertStatus === 'active') {
        query += ` AND aih.issue_resolved_at IS NULL`;
      } else if (alertStatus === 'resolved') {
        query += ` AND aih.issue_resolved_at IS NOT NULL`;
      }
    }

    if (search) {
      query += ` AND (
        ai.name ILIKE $${paramCount}
        OR CAST(ai.id AS TEXT) LIKE $${paramCount}
      )`;
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    if (subscriberId) {
      query += ` AND EXISTS (
        SELECT 1 FROM alert_subscribers
        WHERE alert_id = ai.id
        AND subscriber_id = $${paramCount}
        AND status = 1
      )`;
      queryParams.push(subscriberId);
      paramCount++;
    }

    if (assetType) {
      const assetTypes = assetType.split(',');
      query += ` AND ai.asset_type IN (${assetTypes.map((_, index) =>
        `$${paramCount + index}`).join(',')})`;
      queryParams.push(...assetTypes);
      paramCount += assetTypes.length;
    }

    if (assetId) {
      const assetIds = assetId.split(','); // Support multiple assetIds
      query += ` AND ai.asset_id IN (${assetIds.map((_, index) =>
        `$${paramCount + index}`).join(',')})`;
      queryParams.push(...assetIds);
      paramCount += assetIds.length;
    }

    if (severity) {
      const severities = severity.split(',');
      query += ` AND ai.severity IN (${severities.map((_, index) =>
        `$${paramCount + index}`).join(',')})`;
      queryParams.push(...severities);
      paramCount += severities.length;
    }

    if (durationFilter) {
      const filters = durationFilter.split(',');
      const durationConditions = [];

      if (filters.includes('long')) {
        durationConditions.push('incident_summary.duration_percentage > 75');
      }

      if (filters.includes('medium')) {
        durationConditions.push('incident_summary.duration_percentage BETWEEN 25 AND 75');
      }

      if (filters.includes('short')) {
        durationConditions.push('incident_summary.duration_percentage < 25');
      }

      if (durationConditions.length > 0) {
        query += ` AND (${durationConditions.join(' OR ')})`;
      }
    }

    let orderByClause = '';
    switch (sortBy) {
      case 'name':
        orderByClause = `ai.name ${sortOrder}, aih.issue_occurred_at DESC`;
        break;

      case 'severity':
        orderByClause = `
          CASE ai.severity
            WHEN 'critical' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
            ELSE 5
          END ${sortOrder === 'desc' ? 'ASC' : 'DESC'},
          aih.issue_occurred_at DESC`;
        break;

      case 'duration':
        orderByClause = `incident_summary.total_duration ${sortOrder}, aih.issue_occurred_at DESC`;
        break;

      case 'occurredAt':
        orderByClause = `aih.issue_occurred_at ${sortOrder}`;
        break;

      case 'recentOccurrence':
      default:
        orderByClause = `aih.recent_occurred_event_ts ${sortOrder} NULLS LAST, aih.issue_occurred_at DESC`;
    }

    query += ` ORDER BY ${orderByClause}`;

    const rawAlerts = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).sendNativeQuery(query, queryParams);

    const alertIds = rawAlerts.rows.map(alert => alert.alert_id);
    const subscribers = await AlertSubscribers.find({
      where: { alert_id: { in: alertIds }, status: 1 },
      select: ['alert_id', 'subscriber_id']
    });

    const subscribersMap = subscribers.reduce((map, subscriber) => {
      if (!map[subscriber.alert_id]) {
        map[subscriber.alert_id] = [];
      }
      map[subscriber.alert_id].push(subscriber.subscriber_id);
      return map;
    }, {});

    const alerts = rawAlerts.rows.map(alert => {
      const stringTemplateReplacementMap = {
        paramMap: {
          component: {
            id: alert.device_id,
          },
          site: {
            id: siteId
          }
        }
      }
      return {
        incidentId: alert.incident_id,
        alertId: alert.alert_id,
        alertGroupId: alert.alert_group_id,
        alertType: alert.alert_type,
        siteId: alert.site_id,
        observedOnAsset: {
          deviceId: alert.device_id,
          deviceType: alert.device_type,
        },
        alertName: sails.helpers.globalParamFormatter.with({ string: alert.alert_name, stringTemplateReplacementMap }),
        description:sails.helpers.globalParamFormatter.with({ string: alert.description, stringTemplateReplacementMap }),
        severity: alert.severity,
        subscriberList: subscribersMap[alert.alert_id] || [],
        lastOccurredAt: moment(alert.issue_occurred_at).tz('UTC').toISOString(),
        isStateful: alert.is_stateful,
        isAcknowledged: alert.is_acknowledged,
        activeDuration: alert.total_duration || 0,
        durationPercentage: alert.duration_percentage || 0,
        escalationTimeInMin: alert.escalation_time_in_min,
        escalatedTo: alert.escalated_to || [],
        acknowledgeBy: alert.acknowledge_by,
        acknowledgeTimestamp: alert.acknowledge_ts,
        occurredEventCount: parseInt(alert.occurred_event_count || 1),
        recentOccurredEventTs: alert.recent_occurred_event_ts ?
          moment(alert.recent_occurred_event_ts).tz('UTC').toISOString() : null
      };
    });

    // After getting the base alerts, update the stateful ones with Redis data
    const redisData = await getBatchRecentOccurrencesFromRedis(alerts);
    alerts.forEach(alert => {
      if (alert.isStateful && redisData) {
        const redisValues = redisData[alert.incidentId];
        if (redisValues?.count) {
          alert.occurredEventCount = redisValues.count;
        }
        if (redisValues?.recentTs) {
          alert.recentOccurredEventTs = moment(redisValues.recentTs).tz('UTC').toISOString();
        }
      }
    });

    if (sortBy === 'recentOccurrence') {
      alerts.sort((a, b) => {
        const timeA = a.recentOccurredEventTs || a.lastOccurredAt;
        const timeB = b.recentOccurredEventTs || b.lastOccurredAt;
        return sortOrder === 'desc'
          ? moment(timeB).valueOf() - moment(timeA).valueOf()
          : moment(timeA).valueOf() - moment(timeB).valueOf();
      });
    }

    return alerts;
  },

  /**
   * Get alert incident details by alert inventory ID
   * @param {number} alertInventoryId - Alert inventory ID
   * @returns {Promise<Object>} Alert incident details
   */
  getAlertCurrentState: async function(alertInventoryId) {
    const query = `
      SELECT
        aih.id as incident_id,
        aih.issue_occurred_at,
        aih.issue_resolved_at,
        aih.occurred_event_count,
        aih.recent_occurred_event_ts,
        CASE
          WHEN aih.issue_resolved_at IS NULL THEN 1
          ELSE 0
        END AS is_stateful,
        ai.siteid as site_id
      FROM
        alert_incident_history aih
      JOIN
        alert_inventory ai ON ai.id = aih.alert_inventory_id
      JOIN
        alert_template at ON at.id = ai.alert_template_ref_id
      WHERE
        aih.alert_inventory_id = $1
        AND at.status = 1
      ORDER BY
        aih.recent_occurred_event_ts DESC NULLS LAST,
        aih.issue_occurred_at DESC
      LIMIT 1
    `;

    const rawResult = await sails.getDatastore(process.env.SMART_ALERT_DB_NAME).sendNativeQuery(query, [alertInventoryId]);

    if (!rawResult.rows || rawResult.rows.length === 0) {
      return {
        incidentId: null,
        issueOccurredAt: null,
        issueResolvedAt: null,
        recentOccurredEventTs: null,
        occurredEventCount: 0
      };
    }

    const incident = rawResult.rows[0];
    const isStateful = incident.is_stateful === 1;
    const issueOccurredAt = moment(incident.issue_occurred_at).tz('UTC').toISOString();

    let result = {
      incidentId: incident.incident_id,
      issueOccurredAt,
      issueResolvedAt: incident.issue_resolved_at ?
        moment(incident.issue_resolved_at).tz('UTC').toISOString() : null,
      recentOccurredEventTs: incident.recent_occurred_event_ts ?
        moment(incident.recent_occurred_event_ts).tz('UTC').toISOString() : issueOccurredAt,
      occurredEventCount: parseInt(incident.occurred_event_count || 1)
    };

    if (isStateful) {
      const alertForRedis = {
        incidentId: incident.incident_id,
        siteId: incident.site_id,
        isStateful: true
      };

      const redisData = await getBatchRecentOccurrencesFromRedis([alertForRedis]);

      if (redisData && redisData[incident.incident_id]) {
        const redisValues = redisData[incident.incident_id];

        if (redisValues.count) {
          result.occurredEventCount = redisValues.count;
        }

        if (redisValues.recentTs && moment(redisValues.recentTs).isValid()) {
          result.recentOccurredEventTs = moment(redisValues.recentTs).tz('UTC').toISOString();
        }
      }
    }

    return result;
  }
};
