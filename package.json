{"name": "recipe-micro-service", "private": true, "version": "0.0.0", "description": "a Sails application", "keywords": [], "dependencies": {"@influxdata/influxdb-client": "^1.21.0", "@novu/node": "^2.0.0-canary.1", "@sailshq/connect-redis": "^3.2.1", "@sailshq/lodash": "3.10.5", "@sailshq/socket.io-redis": "^5.2.0", "@sentry/node": "^6.13.2", "@sentry/tracing": "^6.13.2", "amazon-dax-client": "^1.2.9", "amqplib": "^0.6.0", "async": "^3.2.6", "aws-sdk": "^2.1048.0", "axios": "^0.19.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "crypto-js": "^4.2.0", "csvtojson": "^2.0.10", "deepmerge": "^4.2.2", "dotenv": "latest", "exceljs": "^4.3.0", "fast-xml-parser": "^4.5.0", "flaverr": "^1.10.0", "form-data": "^4.0.0", "google-spreadsheet": "^3.3.0", "googleapis": "^126.0.1", "joi": "^17.5.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^8.5.1", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "mathjs": "^7.2.0", "md5": "^2.2.1", "moment": "^2.24.0", "moment-timezone": "^0.5.27", "mqtt": "^4.0.1", "nodemailer": "^6.4.16", "nodemailer-express-handlebars": "^4.0.0", "p-limit": "^6.2.0", "pino": "^7.0.0-rc.6", "prom-client": "^15.1.3", "sails": "^1.2.3", "sails-dynamo-v1": "^1.0.4", "sails-hook-apianalytics": "^2.0.3", "sails-hook-organics": "^0.16.0", "sails-hook-orm": "^2.1.1", "sails-hook-sockets": "^2.0.0", "sails-postgresql": "^5.0.1", "sails-redis": "^1.0.0", "skipper": "^0.9.4", "uuid": "^3.3.3"}, "devDependencies": {"@types/uuid": "^9.0.4", "chai": "^4.2.0", "eslint": "^7.19.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.22.1", "grunt": "1.0.4", "htmlhint": "0.11.0", "husky": "^5.0.8", "istanbul": "^0.4.5", "lesshint": "6.3.6", "madge": "^6.1.0", "mocha": "^7.1.1", "mochawesome": "^6.1.1", "nyc": "^15.1.0", "sails-hook-grunt": "^4.0.0", "sinon": "^9.0.3", "supertest": "^4.0.2"}, "scripts": {"postinstall": "husky install", "start": "NODE_ENV=production node app.js", "dev": "NODE_ENV=development nodemon --inspect", "test": "npm run lint && npm run unit-tests && echo 'Done.'", "coverage": "nyc --reporter=html mocha --recursive ./test/unit --reporter mochawesome --exit", "lint": "eslint .", "unit-tests": "NODE_ENV=testing ./node_modules/mocha/bin/mocha --timeout 10000 test/lifecycle.test.js test/integration/**/*.test.js test/integration/**/**/*.test.js test/unit/**/*.js", "sync-db": "node scripts/sync-dynamo.js", "deploy": "echo 'This script assumes a dead-simple, opinionated setup on Heroku.' && echo 'But, of course, you can deploy your app anywhere you like.' && echo '(Node.js/Sails.js apps are supported on all modern hosting platforms.)' && echo && echo 'Warning: Specifically, this script assumes you are on the master branch, and that your app can be deployed simply by force-pushing on top of the *deploy* branch.  It will also temporarily use a local *predeploy* branch for preparing assets, that it will delete after it finishes.  Please make sure there is nothing you care about on either of these two branches!!!' && echo '' && echo '' && echo 'Preparing to deploy...' && echo '--' && git status && echo '' && echo '--' && echo 'I hope you are on the master branch and have everything committed/pulled/pushed and are completely up to date and stuff.' && echo '********************************************'  && echo '** IF NOT THEN PLEASE PRESS <CTRL+C> NOW! **' && echo '********************************************' && echo 'Press CTRL+C to cancel.' && echo '(you have five seconds)' && sleep 1 && echo '...4' && sleep 1 && echo '...3' && sleep 1 && echo '...2' && sleep 1 && echo '...1' && sleep 1  && echo '' && echo 'Alright, here we go.  No turning back now!' && echo 'Trying to switch to master branch...' && git checkout master && echo && echo 'OK.  Now wiping node_modules/ and running npm install...' && rm -rf node_modules && rm -rf package-lock.json && npm install && (git add package-lock.json && git commit -am 'AUTOMATED COMMIT: Did fresh npm install before deploying, and it caused something relevant (probably the package-lock.json file) to change!  This commit tracks that change.' || true) && echo 'Deploying as version:' && npm version patch && echo '' && git push origin master && git push --tags && (git branch -D predeploy > /dev/null 2>&1 || true) && git checkout -b predeploy && (echo 'Now building+minifying assets for production...' && echo '(Hang tight, this could take a while.)' && echo && node node_modules/grunt/bin/grunt buildProd || (echo && echo '------------------------------------------' && echo 'IMPORTANT!  IMPORTANT!  IMPORTANT!' && echo 'ERROR: Could not compile assets for production!' && echo && echo 'Attempting to recover automatically by stashing, ' && echo 'switching back to the master branch, and then ' && echo 'deleting the predeploy branch... ' && echo && echo 'After this, please fix the issues logged above' && echo 'and push that up.  Then, try deploying again.' && echo '------------------------------------------' && echo && echo 'Staging, deleting the predeploy branch, and switching back to master...' && git stash && git checkout master && git branch -D predeploy && false)) && mv www .www && git add .www && node -e 'sailsrc = JSON.parse(require(\"fs\").readFileSync(\"./.sailsrc\", \"utf8\"));  if (sailsrc.paths&&sailsrc.paths.public !== undefined || sailsrc.hooks&&sailsrc.hooks.grunt !== undefined) { throw new Error(\"Cannot complete deployment script: .sailsrc file has conflicting contents!  Please throw away this midway-complete deployment, switch back to your original branch (master), remove the conflicting stuff from .sailsrc, then commit and push that up.\"); }  sailsrc.paths = sailsrc.paths || {};  sailsrc.paths.public = \"./.www\";   sailsrc.hooks = sailsrc.hooks || {};  sailsrc.hooks.grunt = false;  require(\"fs\").writeFileSync(\"./.sailsrc\", JSON.stringify(sailsrc))' && git commit -am 'AUTOMATED COMMIT: Automatically bundling compiled assets as part of deploy, updating the EJS layout and .sailsrc file accordingly.' && git push origin predeploy && git checkout master && git push origin +predeploy:deploy && git push --tags && git branch -D predeploy && git push origin :predeploy && echo '' && echo '--' && echo 'OK, done.  It should be live momentarily on your staging environment.' && echo '(if you get impatient, check the Heroku dashboard for status)' && echo && echo 'Staging environment:' && echo ' 🌐–•  https://staging.example.com' && echo '       (hold ⌘ and click to open links in the terminal)' && echo && echo 'Please review that to make sure it looks good.' && echo 'When you are ready to go to production, visit your pipeline on Heroku and press the PROMOTE TO PRODUCTION button.'"}, "main": "app.js", "repository": {"type": "git", "url": "git://github.com/Aman/recipe-micro-service.git"}, "author": "Smart joules", "license": "", "engines": {"node": "^8.9"}}