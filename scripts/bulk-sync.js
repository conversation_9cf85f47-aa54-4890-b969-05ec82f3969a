#!/usr/bin/env node

/**
 * Simple DynamoDB Sync Script
 *
 * This script syncs a single table from us-west-2 (master) to any specified region
 * Usage: node scripts/simple-sync.js <siteId> <tableName> <destinationRegion>
 *
 * Parameters:
 * - siteId: The site ID to sync data for
 * - tableName: The DynamoDB table name to sync
 * - destinationRegion: The AWS region to sync data to (e.g., us-west-1, ap-south-1)
 *
 * Examples:
 * node scripts/simple-sync.js ssh_site devices us-west-1
 * node scripts/simple-sync.js ssh_site components ap-south-1
 */

const AWS = require("aws-sdk");
const readline = require("readline");

// Source region is always us-west-2 (master)
const SOURCE_REGION = "us-west-2";

// Table configurations with their key structures
const TABLE_CONFIGS = {
  sites: { hashKey: "siteId", rangeKey: null },
  devices: { hashKey: "deviceId", rangeKey: null },
  components: { hashKey: "deviceId", rangeKey: null },
  parameters: { hashKey: "siteId", rangeKey: "deviceId_abbr" },
  datadevices: { hashKey: "deviceId", rangeKey: "timestamp" },
  commands: { hashKey: "timestamp", rangeKey: null },
  notifications: { hashKey: "id", rangeKey: "timestamp" },
  modes: { hashKey: "did", rangeKey: "timestamp" },
  baselines: { hashKey: "siteId", rangeKey: "startDate" },
  dailyconsumptions: { hashKey: "siteId", rangeKey: "timestamp" },
  dynamictargets: { hashKey: "siteId", rangeKey: "timestamp" },
  dyanmokeystores: { hashKey: "key", rangeKey: null },
  processes: { hashKey: "processId", rangeKey: null },
  users: { hashKey: "userId", rangeKey: null },
  roles: { hashKey: "roleName", rangeKey: null },
  usercomponentcards: { hashKey: "userId_siteId", rangeKey: "id" },
  ShiftProductionData: { hashKey: "pk", rangeKey: "sk" },
};

class SimpleSyncTool {
  constructor(destinationRegion) {
    this.sourceClient = new AWS.DynamoDB.DocumentClient({ region: SOURCE_REGION });
    this.destClient = new AWS.DynamoDB.DocumentClient({ region: destinationRegion });
    this.destinationRegion = destinationRegion;
  }

  /**
   * Get all items from a table filtered by siteId
   */
  async getTableData(client, tableName, siteId) {
    const config = TABLE_CONFIGS[tableName];
    if (!config) {
      throw new Error(`Unknown table: ${tableName}`);
    }

    let items = [];
    let lastEvaluatedKey = null;

    do {
      const params = {
        TableName: tableName,
        ...(lastEvaluatedKey && { ExclusiveStartKey: lastEvaluatedKey }),
      };

      // Add filter expression for siteId if the table has siteId field
      if (this.tableHasSiteId(tableName)) {
        if (config.hashKey === "siteId") {
          // If siteId is the hash key, use KeyConditionExpression
          params.KeyConditionExpression = "siteId = :siteId";
          params.ExpressionAttributeValues = { ":siteId": siteId };

          const result = await client.query(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
        } else {
          // If siteId is not the hash key, use FilterExpression with scan
          params.FilterExpression = "siteId = :siteId";
          params.ExpressionAttributeValues = { ":siteId": siteId };

          const result = await client.scan(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
        }
      } else {
        // For tables without siteId, scan all items (be careful with this)
        console.warn(`Warning: Table ${tableName} doesn't have siteId field. Syncing all data.`);
        const result = await client.scan(params).promise();
        items = items.concat(result.Items);
        lastEvaluatedKey = result.LastEvaluatedKey;
      }
    } while (lastEvaluatedKey);

    return items;
  }

  /**
   * Check if table has siteId field
   */
  tableHasSiteId(tableName) {
    const tablesWithSiteId = [
      "sites",
      "devices",
      "components",
      "parameters",
      "datadevices",
      "baselines",
      "dailyconsumptions",
      "dynamictargets",
      "processes",
      "notifications",
      "ShiftProductionData",
    ];
    return tablesWithSiteId.includes(tableName);
  }

  /**
   * Batch write items to destination table
   */
  async writeTableData(tableName, items) {
    if (items.length === 0) {
      console.log("No items to sync.");
      return;
    }

    console.log(`Writing ${items.length} items to ${tableName} in ${this.destinationRegion}...`);

    // DynamoDB batch write limit is 25 items
    const batchSize = 25;
    const batches = [];

    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const params = {
        RequestItems: {
          [tableName]: batch.map((item) => ({
            PutRequest: { Item: item },
          })),
        },
      };

      try {
        await this.destClient.batchWrite(params).promise();
        console.log(`Batch ${i + 1}/${batches.length} completed`);
      } catch (error) {
        console.error(`Error writing batch ${i + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Clear existing data for siteId in destination table
   */
  async clearTableData(tableName, siteId) {
    console.log(
      `Clearing existing data for siteId ${siteId} in ${tableName} (${this.destinationRegion})...`
    );

    const existingItems = await this.getTableData(this.destClient, tableName, siteId);
    if (existingItems.length === 0) {
      console.log("No existing data to clear.");
      return;
    }

    const config = TABLE_CONFIGS[tableName];
    const batchSize = 25;

    for (let i = 0; i < existingItems.length; i += batchSize) {
      const batch = existingItems.slice(i, i + batchSize);
      const params = {
        RequestItems: {
          [tableName]: batch.map((item) => {
            const key = { [config.hashKey]: item[config.hashKey] };
            if (config.rangeKey) {
              key[config.rangeKey] = item[config.rangeKey];
            }
            return { DeleteRequest: { Key: key } };
          }),
        },
      };

      try {
        await this.destClient.batchWrite(params).promise();
        console.log(`Deleted batch ${Math.floor(i / batchSize) + 1}`);
      } catch (error) {
        console.error(`Error deleting batch:`, error);
        throw error;
      }
    }
  }

  /**
   * Ask for user confirmation
   */
  async confirmSync(siteId, tableName, itemCount) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise((resolve) => {
      rl.question(
        `\n⚠️  WARNING: This will replace ${itemCount} items in ${this.destinationRegion} ${tableName} table for siteId "${siteId}".\n` +
          `Source: ${SOURCE_REGION} (master)\n` +
          `Destination: ${this.destinationRegion}\n` +
          `Are you sure you want to continue? (yes/no): `,
        (answer) => {
          rl.close();
          resolve(answer.toLowerCase() === "yes" || answer.toLowerCase() === "y");
        }
      );
    });
  }

  /**
   * Main sync function
   */
  async sync(siteId, tableName) {
    console.log(`\n=== Simple DynamoDB Sync Tool ===`);
    console.log(`Site ID: ${siteId}`);
    console.log(`Table: ${tableName}`);
    console.log(`Source: ${SOURCE_REGION} (master)`);
    console.log(`Destination: ${this.destinationRegion}\n`);

    if (!TABLE_CONFIGS[tableName]) {
      throw new Error(
        `Unknown table: ${tableName}. Available tables: ${Object.keys(TABLE_CONFIGS).join(", ")}`
      );
    }

    try {
      // Get data from source (master)
      console.log(`Fetching data from ${SOURCE_REGION} (master)...`);
      const sourceData = await this.getTableData(this.sourceClient, tableName, siteId);
      console.log(`Found ${sourceData.length} items in source.`);

      if (sourceData.length === 0) {
        console.log("No data found to sync.");
        return;
      }

      // Confirm before proceeding
      const confirmed = await this.confirmSync(siteId, tableName, sourceData.length);
      if (!confirmed) {
        console.log("Sync cancelled.");
        return;
      }

      // Clear existing data in destination
      await this.clearTableData(tableName, siteId);

      // Write data to destination
      await this.writeTableData(tableName, sourceData);

      console.log(`\n✅ Sync completed successfully!`);
      console.log(
        `Synced ${sourceData.length} items from ${SOURCE_REGION} to ${this.destinationRegion}`
      );
    } catch (error) {
      console.error("\n❌ Sync failed:", error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.length !== 3) {
    console.log(`
Usage: node scripts/simple-sync.js <siteId> <tableName> <destinationRegion>

Parameters:
  siteId            - The site ID to sync data for
  tableName         - The DynamoDB table name to sync
  destinationRegion - The AWS region to sync data to

Available tables:
  ${Object.keys(TABLE_CONFIGS).join(", ")}

Examples:
  node scripts/simple-sync.js ssh_site devices us-west-1
  node scripts/simple-sync.js ssh_site components ap-south-1
  node scripts/simple-sync.js ssh_site parameters us-east-1

Note: Data is always synced FROM us-west-2 (master) TO the specified region.
    `);
    process.exit(1);
  }

  const [siteId, tableName, destinationRegion] = args;

  const syncTool = new SimpleSyncTool(destinationRegion);

  try {
    await syncTool.sync(siteId, tableName);
  } catch (error) {
    console.error("Sync failed:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = SimpleSyncTool;
