#!/usr/bin/env node

/**
 * Bulk DynamoDB Sync Script
 * 
 * This script syncs multiple tables for a given siteId between environments
 * Usage: node scripts/bulk-sync.js <siteId> [direction] [tables...]
 * 
 * Parameters:
 * - siteId: The site ID to sync data for
 * - direction: 'staging-to-master' (default) or 'master-to-staging'
 * - tables: Space-separated list of table names (optional, defaults to core tables)
 * 
 * Examples:
 * node scripts/bulk-sync.js ssh_site
 * node scripts/bulk-sync.js ssh_site staging-to-master
 * node scripts/bulk-sync.js ssh_site master-to-staging sites devices components
 */

const DynamoDBSyncTool = require('./dynamodb-sync.js');
const readline = require('readline');

// Default core tables to sync (in order of dependencies)
const DEFAULT_TABLES = [
  'sites',
  'devices', 
  'components',
  'parameters',
  'baselines',
  'dailyconsumptions',
  'dynamictargets',
  'processes'
];

// All available tables
const ALL_TABLES = [
  'sites',
  'devices',
  'components', 
  'parameters',
  'datadevices',
  'commands',
  'notifications',
  'modes',
  'baselines',
  'dailyconsumptions',
  'dynamictargets',
  'dyanmokeystores',
  'processes',
  'users',
  'roles',
  'usercomponentcards',
  'ShiftProductionData'
];

class BulkSyncTool {
  constructor() {
    this.syncTool = new DynamoDBSyncTool();
  }

  /**
   * Ask for user confirmation for bulk sync
   */
  async confirmBulkSync(siteId, tables, direction) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      console.log(`\n=== Bulk Sync Configuration ===`);
      console.log(`Site ID: ${siteId}`);
      console.log(`Direction: ${direction}`);
      console.log(`Tables to sync (${tables.length}):`);
      tables.forEach((table, index) => {
        console.log(`  ${index + 1}. ${table}`);
      });
      
      rl.question(
        `\n⚠️  WARNING: This will sync ${tables.length} tables for siteId "${siteId}".\n` +
        `Each table will have its existing data replaced in the destination environment.\n` +
        `Are you sure you want to continue? (yes/no): `,
        (answer) => {
          rl.close();
          resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
        }
      );
    });
  }

  /**
   * Sync multiple tables in sequence
   */
  async bulkSync(siteId, direction = 'staging-to-master', tables = DEFAULT_TABLES) {
    console.log(`\n=== DynamoDB Bulk Sync Tool ===`);
    
    // Validate tables
    const invalidTables = tables.filter(table => !ALL_TABLES.includes(table));
    if (invalidTables.length > 0) {
      throw new Error(`Invalid tables: ${invalidTables.join(', ')}. Available tables: ${ALL_TABLES.join(', ')}`);
    }

    // Confirm bulk sync
    const confirmed = await this.confirmBulkSync(siteId, tables, direction);
    if (!confirmed) {
      console.log('Bulk sync cancelled.');
      return;
    }

    const results = {
      successful: [],
      failed: [],
      startTime: new Date(),
      endTime: null
    };

    console.log(`\n🚀 Starting bulk sync of ${tables.length} tables...\n`);

    // Sync each table
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      const tableNum = i + 1;
      
      console.log(`\n[${tableNum}/${tables.length}] Syncing table: ${table}`);
      console.log('='.repeat(50));
      
      try {
        // Use a modified sync method that doesn't ask for confirmation
        await this.syncTableWithoutConfirmation(siteId, table, direction);
        results.successful.push(table);
        console.log(`✅ Successfully synced ${table}`);
      } catch (error) {
        console.error(`❌ Failed to sync ${table}:`, error.message);
        results.failed.push({ table, error: error.message });
        
        // Ask if user wants to continue with remaining tables
        if (i < tables.length - 1) {
          const shouldContinue = await this.askContinue(table, tables.length - i - 1);
          if (!shouldContinue) {
            console.log('Bulk sync stopped by user.');
            break;
          }
        }
      }
    }

    results.endTime = new Date();
    this.printSummary(results);
  }

  /**
   * Sync a single table without confirmation prompt
   */
  async syncTableWithoutConfirmation(siteId, tableName, direction) {
    const isStgToMaster = direction === 'staging-to-master';
    const sourceClient = isStgToMaster ? this.syncTool.stagingClient : this.syncTool.masterClient;
    const destClient = isStgToMaster ? this.syncTool.masterClient : this.syncTool.stagingClient;
    const sourceEnv = isStgToMaster ? 'staging' : 'master';

    // Get data from source
    console.log(`Fetching data from ${sourceEnv}...`);
    const sourceData = await this.syncTool.getTableData(sourceClient, tableName, siteId);
    console.log(`Found ${sourceData.length} items in source.`);

    if (sourceData.length === 0) {
      console.log('No data found to sync.');
      return;
    }

    // Clear existing data in destination
    await this.syncTool.clearTableData(destClient, tableName, siteId);

    // Write data to destination
    await this.syncTool.writeTableData(destClient, tableName, sourceData);
  }

  /**
   * Ask if user wants to continue after a failure
   */
  async askContinue(failedTable, remainingCount) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(
        `\nTable "${failedTable}" failed to sync. ${remainingCount} tables remaining.\n` +
        `Do you want to continue with the remaining tables? (yes/no): `,
        (answer) => {
          rl.close();
          resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
        }
      );
    });
  }

  /**
   * Print sync summary
   */
  printSummary(results) {
    const duration = (results.endTime - results.startTime) / 1000;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 BULK SYNC SUMMARY');
    console.log('='.repeat(60));
    console.log(`Duration: ${duration.toFixed(2)} seconds`);
    console.log(`Total tables: ${results.successful.length + results.failed.length}`);
    console.log(`✅ Successful: ${results.successful.length}`);
    console.log(`❌ Failed: ${results.failed.length}`);
    
    if (results.successful.length > 0) {
      console.log('\n✅ Successfully synced tables:');
      results.successful.forEach(table => console.log(`  - ${table}`));
    }
    
    if (results.failed.length > 0) {
      console.log('\n❌ Failed tables:');
      results.failed.forEach(({ table, error }) => {
        console.log(`  - ${table}: ${error}`);
      });
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log(`
Usage: node scripts/bulk-sync.js <siteId> [direction] [tables...]

Parameters:
  siteId     - The site ID to sync data for (required)
  direction  - 'staging-to-master' (default) or 'master-to-staging'
  tables     - Space-separated list of table names (optional)

Available tables:
  ${ALL_TABLES.join(', ')}

Default tables (if none specified):
  ${DEFAULT_TABLES.join(', ')}

Examples:
  # Sync default core tables from staging to master
  node scripts/bulk-sync.js ssh_site

  # Sync all default tables from master to staging  
  node scripts/bulk-sync.js ssh_site master-to-staging

  # Sync specific tables
  node scripts/bulk-sync.js ssh_site staging-to-master sites devices components

  # Sync all tables
  node scripts/bulk-sync.js ssh_site staging-to-master ${ALL_TABLES.join(' ')}
    `);
    process.exit(1);
  }

  let siteId, direction, tables;
  
  if (args.length === 1) {
    // Only siteId provided
    [siteId] = args;
    direction = 'staging-to-master';
    tables = DEFAULT_TABLES;
  } else if (args.length === 2) {
    // siteId and direction provided
    [siteId, direction] = args;
    tables = DEFAULT_TABLES;
  } else {
    // siteId, direction, and tables provided
    [siteId, direction, ...tables] = args;
  }

  if (!['staging-to-master', 'master-to-staging'].includes(direction)) {
    console.error('Invalid direction. Use "staging-to-master" or "master-to-staging"');
    process.exit(1);
  }

  const bulkSyncTool = new BulkSyncTool();
  
  try {
    await bulkSyncTool.bulkSync(siteId, direction, tables);
  } catch (error) {
    console.error('Bulk sync failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = BulkSyncTool;
