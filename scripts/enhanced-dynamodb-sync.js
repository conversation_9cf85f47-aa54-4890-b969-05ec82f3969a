#!/usr/bin/env node

/**
 * Enhanced DynamoDB Sync Script
 *
 * This script syncs DynamoDB tables from us-west-2 (master) to any specified region
 *
 * Features:
 * - Single table sync
 * - Multiple table sync
 * - Dry run mode
 * - Better error handling and logging
 * - Progress tracking
 * - Rollback capabilities
 *
 * Usage:
 * node scripts/enhanced-dynamodb-sync.js <siteId> <tableName> <destinationRegion> [options]
 * node scripts/enhanced-dynamodb-sync.js <siteId> --all <destinationRegion> [options]
 *
 * Options:
 * --dry-run    : Show what would be synced without making changes
 * --all        : Sync all tables for the site
 * --force      : Skip confirmation prompts
 * --verbose    : Detailed logging
 *
 * Examples:
 * node scripts/enhanced-dynamodb-sync.js ssh_site devices us-west-1
 * node scripts/enhanced-dynamodb-sync.js ssh_site --all us-west-1 --dry-run
 * node scripts/enhanced-dynamodb-sync.js ssh_site components ap-south-1 --force
 */

const AWS = require("aws-sdk");
const readline = require("readline");
const winston = require("winston");

// Source region is always us-west-2 (master)
const SOURCE_REGION = "us-west-2";

// Table configurations with their key structures and metadata
const TABLE_CONFIGS = {
  // Core business tables
  sites: {
    hashKey: "siteId",
    rangeKey: null,
    category: "core",
    description: "Site configuration data",
    priority: 1,
  },
  devices: {
    hashKey: "deviceId",
    rangeKey: null,
    category: "core",
    description: "Physical/virtual devices",
    priority: 2,
  },
  components: {
    hashKey: "deviceId",
    rangeKey: null,
    category: "core",
    description: "Component configurations",
    priority: 3,
  },
  parameters: {
    hashKey: "siteId",
    rangeKey: "deviceId_abbr",
    category: "core",
    description: "Device parameter definitions",
    priority: 4,
  },

  // Data and time-series tables
  datadevices: {
    hashKey: "deviceId",
    rangeKey: "timestamp",
    category: "data",
    description: "Device time-series data",
    priority: 10,
    isLargeTable: true,
  },
  commands: {
    hashKey: "timestamp",
    rangeKey: null,
    category: "data",
    description: "Command history",
    priority: 8,
  },
  notifications: {
    hashKey: "id",
    rangeKey: "timestamp",
    category: "data",
    description: "System notifications",
    priority: 9,
  },
  modes: {
    hashKey: "did",
    rangeKey: "timestamp",
    category: "data",
    description: "Device mode history",
    priority: 9,
  },

  // Analytics tables
  baselines: {
    hashKey: "siteId",
    rangeKey: "startDate",
    category: "analytics",
    description: "Energy consumption baselines",
    priority: 6,
  },
  dailyconsumptions: {
    hashKey: "siteId",
    rangeKey: "timestamp",
    category: "analytics",
    description: "Daily consumption data",
    priority: 7,
    isLargeTable: true,
  },
  dynamictargets: {
    hashKey: "siteId",
    rangeKey: "timestamp",
    category: "analytics",
    description: "Dynamic target values",
    priority: 7,
  },
  ShiftProductionData: {
    hashKey: "pk",
    rangeKey: "sk",
    category: "analytics",
    description: "Production data by shifts",
    priority: 7,
  },

  // System configuration
  dyanmokeystores: {
    hashKey: "key",
    rangeKey: null,
    category: "config",
    description: "Key-value configuration store",
    priority: 1,
  },
  processes: {
    hashKey: "processId",
    rangeKey: null,
    category: "config",
    description: "Process definitions",
    priority: 5,
  },

  // User management
  users: {
    hashKey: "userId",
    rangeKey: null,
    category: "user",
    description: "User accounts",
    priority: 1,
    requiresSpecialHandling: true,
  },
  roles: {
    hashKey: "roleName",
    rangeKey: null,
    category: "user",
    description: "User roles and permissions",
    priority: 1,
    requiresSpecialHandling: true,
  },
  usersitemaps: {
    hashKey: "userId_siteId",
    rangeKey: null,
    category: "user",
    description: "User-site access mapping",
    priority: 2,
  },
  usercomponentcards: {
    hashKey: "userId_siteId",
    rangeKey: "id",
    category: "user",
    description: "User dashboard configurations",
    priority: 3,
  },
};

// Tables that contain siteId and can be filtered by site
const SITE_FILTERED_TABLES = [
  "sites",
  "devices",
  "components",
  "parameters",
  "datadevices",
  "baselines",
  "dailyconsumptions",
  "dynamictargets",
  "ShiftProductionData",
  "processes",
  "notifications",
  "usersitemaps",
];

// Configure logger
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level.toUpperCase()}]: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "dynamodb-sync.log" }),
  ],
});

class EnhancedDynamoSyncTool {
  constructor(destinationRegion, options = {}) {
    this.sourceClient = new AWS.DynamoDB.DocumentClient({ region: SOURCE_REGION });
    this.destClient = new AWS.DynamoDB.DocumentClient({ region: destinationRegion });
    this.destinationRegion = destinationRegion;
    this.options = {
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      force: options.force || false,
      ...options,
    };

    if (this.options.verbose) {
      logger.level = "debug";
    }
  }

  /**
   * Get all items from a table filtered by siteId
   */
  async getTableData(client, tableName, siteId) {
    const config = TABLE_CONFIGS[tableName];
    if (!config) {
      throw new Error(`Unknown table: ${tableName}`);
    }

    logger.debug(`Fetching data from ${tableName} for siteId: ${siteId}`);

    let items = [];
    let lastEvaluatedKey = null;
    let scanCount = 0;

    do {
      scanCount++;
      const params = {
        TableName: tableName,
        ...(lastEvaluatedKey && { ExclusiveStartKey: lastEvaluatedKey }),
      };

      // Add filter expression for siteId if the table has siteId field
      if (this.tableHasSiteId(tableName)) {
        if (config.hashKey === "siteId") {
          // If siteId is the hash key, use KeyConditionExpression
          params.KeyConditionExpression = "siteId = :siteId";
          params.ExpressionAttributeValues = { ":siteId": siteId };

          const result = await client.query(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
          logger.debug(`Query ${scanCount}: Retrieved ${result.Items.length} items`);
        } else if (tableName === "usersitemaps") {
          // Special handling for usersitemaps - filter by userId_siteId contains siteId
          params.FilterExpression = "contains(userId_siteId, :siteId)";
          params.ExpressionAttributeValues = { ":siteId": `_${siteId}` };

          const result = await client.scan(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
          logger.debug(`Scan ${scanCount}: Retrieved ${result.Items.length} items`);
        } else {
          // If siteId is not the hash key, use FilterExpression with scan
          params.FilterExpression = "siteId = :siteId";
          params.ExpressionAttributeValues = { ":siteId": siteId };

          const result = await client.scan(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
          logger.debug(`Scan ${scanCount}: Retrieved ${result.Items.length} items`);
        }
      } else {
        // For tables without siteId, we need special handling or skip
        if (tableName === "users" || tableName === "roles") {
          logger.warn(
            `Warning: Table ${tableName} doesn't have siteId field. This requires special handling.`
          );
          return []; // Return empty array for now
        } else {
          logger.warn(`Warning: Table ${tableName} doesn't have siteId field. Syncing all data.`);
          const result = await client.scan(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
          logger.debug(`Scan ${scanCount}: Retrieved ${result.Items.length} items`);
        }
      }
    } while (lastEvaluatedKey);

    logger.info(`Fetched ${items.length} items from ${tableName} (${scanCount} operations)`);
    return items;
  }

  /**
   * Check if table has siteId field
   */
  tableHasSiteId(tableName) {
    return SITE_FILTERED_TABLES.includes(tableName);
  }

  /**
   * Batch write items to destination table
   */
  async writeTableData(tableName, items) {
    if (items.length === 0) {
      logger.info("No items to sync.");
      return;
    }

    if (this.options.dryRun) {
      logger.info(
        `[DRY RUN] Would write ${items.length} items to ${tableName} in ${this.destinationRegion}`
      );
      return;
    }

    logger.info(`Writing ${items.length} items to ${tableName} in ${this.destinationRegion}...`);

    // DynamoDB batch write limit is 25 items
    const batchSize = 25;
    const batches = [];

    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const params = {
        RequestItems: {
          [tableName]: batch.map((item) => ({
            PutRequest: { Item: item },
          })),
        },
      };

      try {
        await this.destClient.batchWrite(params).promise();
        logger.debug(`Batch ${i + 1}/${batches.length} completed`);

        // Progress indicator for large batches
        if (batches.length > 10 && (i + 1) % 10 === 0) {
          logger.info(`Progress: ${i + 1}/${batches.length} batches completed`);
        }
      } catch (error) {
        logger.error(`Error writing batch ${i + 1}:`, error);
        throw error;
      }
    }

    logger.info(`Successfully wrote ${items.length} items to ${tableName}`);
  }

  /**
   * Clear existing data for siteId in destination table
   */
  async clearTableData(tableName, siteId) {
    if (this.options.dryRun) {
      const existingItems = await this.getTableData(this.destClient, tableName, siteId);
      logger.info(
        `[DRY RUN] Would clear ${existingItems.length} existing items for siteId ${siteId} in ${tableName}`
      );
      return;
    }

    logger.info(
      `Clearing existing data for siteId ${siteId} in ${tableName} (${this.destinationRegion})...`
    );

    const existingItems = await this.getTableData(this.destClient, tableName, siteId);
    if (existingItems.length === 0) {
      logger.info("No existing data to clear.");
      return;
    }

    const config = TABLE_CONFIGS[tableName];
    const batchSize = 25;

    for (let i = 0; i < existingItems.length; i += batchSize) {
      const batch = existingItems.slice(i, i + batchSize);
      const params = {
        RequestItems: {
          [tableName]: batch.map((item) => {
            const key = { [config.hashKey]: item[config.hashKey] };
            if (config.rangeKey) {
              key[config.rangeKey] = item[config.rangeKey];
            }
            return { DeleteRequest: { Key: key } };
          }),
        },
      };

      try {
        await this.destClient.batchWrite(params).promise();
        logger.debug(`Deleted batch ${Math.floor(i / batchSize) + 1}`);
      } catch (error) {
        logger.error(`Error deleting batch:`, error);
        throw error;
      }
    }

    logger.info(`Cleared ${existingItems.length} existing items from ${tableName}`);
  }

  /**
   * Ask for user confirmation
   */
  async confirmSync(siteId, tableName, itemCount) {
    if (this.options.force || this.options.dryRun) {
      return true;
    }

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise((resolve) => {
      rl.question(
        `\n⚠️  WARNING: This will replace ${itemCount} items in ${this.destinationRegion} ${tableName} table for siteId "${siteId}".\n` +
          `Source: ${SOURCE_REGION} (master)\n` +
          `Destination: ${this.destinationRegion}\n` +
          `Are you sure you want to continue? (yes/no): `,
        (answer) => {
          rl.close();
          resolve(answer.toLowerCase() === "yes" || answer.toLowerCase() === "y");
        }
      );
    });
  }

  /**
   * Sync a single table
   */
  async syncTable(siteId, tableName) {
    const config = TABLE_CONFIGS[tableName];

    logger.info(`\n=== Syncing ${tableName} ===`);
    logger.info(`Site ID: ${siteId}`);
    logger.info(`Table: ${tableName} (${config.description})`);
    logger.info(`Source: ${SOURCE_REGION} (master)`);
    logger.info(`Destination: ${this.destinationRegion}`);
    logger.info(`Mode: ${this.options.dryRun ? "DRY RUN" : "LIVE"}\n`);

    try {
      // Get data from source (master)
      logger.info(`Fetching data from ${SOURCE_REGION} (master)...`);
      const sourceData = await this.getTableData(this.sourceClient, tableName, siteId);
      logger.info(`Found ${sourceData.length} items in source.`);

      if (sourceData.length === 0) {
        logger.info("No data found to sync.");
        return { success: true, itemCount: 0 };
      }

      // Warn for large tables
      if (config.isLargeTable && sourceData.length > 1000) {
        logger.warn(`⚠️  Large table detected: ${sourceData.length} items. This may take a while.`);
      }

      // Confirm before proceeding
      const confirmed = await this.confirmSync(siteId, tableName, sourceData.length);
      if (!confirmed) {
        logger.info("Sync cancelled.");
        return { success: false, itemCount: 0, cancelled: true };
      }

      // Clear existing data in destination
      await this.clearTableData(tableName, siteId);

      // Write data to destination
      await this.writeTableData(tableName, sourceData);

      const message = this.options.dryRun
        ? `✅ Dry run completed for ${tableName}`
        : `✅ Sync completed successfully for ${tableName}`;

      logger.info(message);
      logger.info(`Items processed: ${sourceData.length}`);

      return { success: true, itemCount: sourceData.length };
    } catch (error) {
      logger.error(`❌ Sync failed for ${tableName}:`, error.message);
      return { success: false, error: error.message, itemCount: 0 };
    }
  }

  /**
   * Sync multiple tables
   */
  async syncMultipleTables(siteId, tableNames) {
    logger.info(`\n=== Enhanced DynamoDB Multi-Table Sync ===`);
    logger.info(`Site ID: ${siteId}`);
    logger.info(`Tables: ${tableNames.join(", ")}`);
    logger.info(`Source: ${SOURCE_REGION} (master)`);
    logger.info(`Destination: ${this.destinationRegion}`);
    logger.info(`Mode: ${this.options.dryRun ? "DRY RUN" : "LIVE"}\n`);

    const results = [];
    let totalItems = 0;
    let successfulTables = 0;

    // Sort tables by priority
    const sortedTables = tableNames.sort((a, b) => {
      const priorityA = TABLE_CONFIGS[a]?.priority || 999;
      const priorityB = TABLE_CONFIGS[b]?.priority || 999;
      return priorityA - priorityB;
    });

    logger.info(`Sync order (by priority): ${sortedTables.join(" → ")}`);

    for (const tableName of sortedTables) {
      const result = await this.syncTable(siteId, tableName);
      results.push({ tableName, ...result });

      if (result.success) {
        successfulTables++;
        totalItems += result.itemCount;
      }

      // Small delay between tables to avoid overwhelming DynamoDB
      if (!this.options.dryRun) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Summary
    logger.info(`\n=== Sync Summary ===`);
    logger.info(`Successful tables: ${successfulTables}/${tableNames.length}`);
    logger.info(`Total items processed: ${totalItems}`);

    results.forEach((result) => {
      const status = result.success ? "✅" : "❌";
      const items = result.cancelled ? "cancelled" : `${result.itemCount} items`;
      logger.info(`${status} ${result.tableName}: ${items}`);
    });

    return results;
  }

  /**
   * List all available tables with metadata
   */
  listTables() {
    logger.info("\n=== Available Tables ===");

    const categories = {};
    Object.entries(TABLE_CONFIGS).forEach(([tableName, config]) => {
      if (!categories[config.category]) {
        categories[config.category] = [];
      }
      categories[config.category].push({ tableName, config });
    });

    Object.entries(categories).forEach(([category, tables]) => {
      logger.info(`\n${category.toUpperCase()} TABLES:`);
      tables
        .sort((a, b) => a.config.priority - b.config.priority)
        .forEach(({ tableName, config }) => {
          const flags = [];
          if (config.isLargeTable) flags.push("large");
          if (config.requiresSpecialHandling) flags.push("special");
          const flagStr = flags.length ? ` [${flags.join(", ")}]` : "";

          logger.info(`  • ${tableName}${flagStr} - ${config.description}`);
        });
    });
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  // Parse options
  const options = {
    dryRun: args.includes("--dry-run"),
    verbose: args.includes("--verbose"),
    force: args.includes("--force"),
    all: args.includes("--all"),
    list: args.includes("--list"),
  };

  // Remove options from args
  const cleanArgs = args.filter((arg) => !arg.startsWith("--"));

  if (options.list) {
    const syncTool = new EnhancedDynamoSyncTool("us-west-1", options);
    syncTool.listTables();
    return;
  }

  if (cleanArgs.length < 2) {
    console.log(`
Enhanced DynamoDB Sync Tool

Usage:
  node scripts/enhanced-dynamodb-sync.js <siteId> <tableName> <destinationRegion> [options]
  node scripts/enhanced-dynamodb-sync.js <siteId> --all <destinationRegion> [options]

Parameters:
  siteId            - The site ID to sync data for
  tableName         - The DynamoDB table name to sync (or --all for all tables)
  destinationRegion - The AWS region to sync data to

Options:
  --dry-run         - Show what would be synced without making changes
  --all             - Sync all tables for the site (ordered by priority)
  --force           - Skip confirmation prompts
  --verbose         - Detailed logging
  --list            - List all available tables

Examples:
  node scripts/enhanced-dynamodb-sync.js ssh_site devices us-west-1
  node scripts/enhanced-dynamodb-sync.js ssh_site --all us-west-1 --dry-run
  node scripts/enhanced-dynamodb-sync.js ssh_site components ap-south-1 --force --verbose
  node scripts/enhanced-dynamodb-sync.js --list

Core sync order (by priority):
  1. sites, dyanmokeystores, users, roles
  2. devices, usersitemaps
  3. components, usercomponentcards
  4. parameters
  5. processes
  6. baselines
  7. analytics tables (dailyconsumptions, dynamictargets, etc.)
  8. commands
  9. notifications, modes
  10. datadevices (large table - use with caution)

Note: Data is always synced FROM us-west-2 (master) TO the specified region.
    `);
    process.exit(1);
  }

  const [siteId, tableNameOrRegion, destinationRegion] = cleanArgs;

  // Handle --all option
  if (options.all) {
    const actualDestinationRegion = tableNameOrRegion;
    const syncTool = new EnhancedDynamoSyncTool(actualDestinationRegion, options);

    // Get all table names sorted by priority
    const allTables = Object.keys(TABLE_CONFIGS);

    try {
      await syncTool.syncMultipleTables(siteId, allTables);
    } catch (error) {
      logger.error("Multi-table sync failed:", error);
      process.exit(1);
    }
    return;
  }

  // Single table sync
  const tableName = tableNameOrRegion;

  if (!TABLE_CONFIGS[tableName]) {
    console.error(`Unknown table: ${tableName}`);
    console.log(`Available tables: ${Object.keys(TABLE_CONFIGS).join(", ")}`);
    process.exit(1);
  }

  const syncTool = new EnhancedDynamoSyncTool(destinationRegion, options);

  try {
    await syncTool.syncTable(siteId, tableName);
  } catch (error) {
    logger.error("Sync failed:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = EnhancedDynamoSyncTool;
