#!/usr/bin/env node

/**
 * Simple DynamoDB Sync Script
 *
 * This script syncs a single table from us-west-2 (master) to any specified region
 * Usage: node scripts/sync-dynamo.js <partitionKeyValue> <tableName> <destinationRegion>
 *
 * Parameters:
 * - partitionKeyValue: The value of the partition key to sync data for
 * - tableName: The DynamoDB table name to sync
 * - destinationRegion: The AWS region to sync data to (e.g., us-west-1, ap-south-1)
 *
 * Examples:
 * node scripts/sync-dynamo.js ssh_site sites us-west-1          # siteId = ssh_site
 * node scripts/sync-dynamo.js device123 devices us-west-1       # deviceId = device123
 * node scripts/sync-dynamo.js user456 users ap-south-1          # userId = user456
 */
const AWS = require("aws-sdk");
const readline = require("readline");
require("dotenv").config();

// Source region is always us-west-2 (master)
const SOURCE_REGION = "us-west-2";

// Configure AWS SDK to load config from files
AWS.config.update({
  region: SOURCE_REGION,
});

// Table configurations with their key structures
const TABLE_CONFIGS = {
  // Core business tables
  sites: { hashKey: "siteId", rangeKey: null },
  devices: { hashKey: "deviceId", rangeKey: null },
  components: { hashKey: "deviceId", rangeKey: null },
  parameters: { hashKey: "siteId", rangeKey: "deviceId_abbr" },

  // Data and time-series tables
  datadevices: { hashKey: "deviceId", rangeKey: "timestamp" },
  commands: { hashKey: "timestamp", rangeKey: null },
  notifications: { hashKey: "id", rangeKey: "timestamp" },
  modes: { hashKey: "did", rangeKey: "timestamp" },

  // Analytics tables
  baselines: { hashKey: "siteId", rangeKey: "startDate" },
  dailyconsumptions: { hashKey: "siteId", rangeKey: "timestamp" },
  dynamictargets: { hashKey: "siteId", rangeKey: "timestamp" },
  ShiftProductionData: { hashKey: "pk", rangeKey: "sk" },

  // System configuration
  dyanmokeystores: { hashKey: "key", rangeKey: null },
  processes: { hashKey: "processId", rangeKey: null },

  // User management
  users: { hashKey: "userId", rangeKey: null },
  roles: { hashKey: "roleName", rangeKey: null },
  usersitemaps: { hashKey: "userId_siteId", rangeKey: null },
  usercomponentcards: { hashKey: "userId_siteId", rangeKey: "id" },
};

class SimpleSyncTool {
  constructor(destinationRegion) {
    this.sourceClient = new AWS.DynamoDB.DocumentClient({ region: SOURCE_REGION });
    this.destClient = new AWS.DynamoDB.DocumentClient({ region: destinationRegion });
    this.destinationRegion = destinationRegion;
  }

  /**
   * Get all items from a table filtered by partition key value
   */
  async getTableData(client, tableName, partitionKeyValue) {
    const config = TABLE_CONFIGS[tableName];
    if (!config) {
      throw new Error(`Unknown table: ${tableName}`);
    }

    let items = [];
    let lastEvaluatedKey = null;

    do {
      const params = {
        TableName: tableName,
        ...(lastEvaluatedKey && { ExclusiveStartKey: lastEvaluatedKey }),
      };

      // Use partition key for efficient querying
      params.KeyConditionExpression = `${config.hashKey} = :partitionKeyValue`;
      params.ExpressionAttributeValues = { ":partitionKeyValue": partitionKeyValue };

      const result = await client.query(params).promise();
      items = items.concat(result.Items);
      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    return items;
  }

  /**
   * Batch write items to destination table
   */
  async writeTableData(tableName, items) {
    if (items.length === 0) {
      console.log("No items to sync.");
      return;
    }

    console.log(`Writing ${items.length} items to ${tableName} in ${this.destinationRegion}...`);

    // DynamoDB batch write limit is 25 items
    const batchSize = 25;
    const batches = [];

    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const params = {
        RequestItems: {
          [tableName]: batch.map((item) => ({
            PutRequest: { Item: item },
          })),
        },
      };

      try {
        await this.destClient.batchWrite(params).promise();
        console.log(`Batch ${i + 1}/${batches.length} completed`);
      } catch (error) {
        console.error(`Error writing batch ${i + 1}:`, error);
        throw error;
      }
    }
  }

  /**
   * Clear existing data for partition key value in destination table
   */
  async clearTableData(tableName, partitionKeyValue) {
    const config = TABLE_CONFIGS[tableName];
    console.log(
      `Clearing existing data for ${config.hashKey} = ${partitionKeyValue} in ${tableName} (${this.destinationRegion})...`
    );

    const existingItems = await this.getTableData(this.destClient, tableName, partitionKeyValue);
    if (existingItems.length === 0) {
      console.log("No existing data to clear.");
      return;
    }
    const batchSize = 25;

    for (let i = 0; i < existingItems.length; i += batchSize) {
      const batch = existingItems.slice(i, i + batchSize);
      const params = {
        RequestItems: {
          [tableName]: batch.map((item) => {
            const key = { [config.hashKey]: item[config.hashKey] };
            if (config.rangeKey) {
              key[config.rangeKey] = item[config.rangeKey];
            }
            return { DeleteRequest: { Key: key } };
          }),
        },
      };

      try {
        await this.destClient.batchWrite(params).promise();
        console.log(`Deleted batch ${Math.floor(i / batchSize) + 1}`);
      } catch (error) {
        console.error(`Error deleting batch:`, error);
        throw error;
      }
    }
  }

  /**
   * Ask for user confirmation
   */
  async confirmSync(partitionKeyValue, tableName, itemCount) {
    const config = TABLE_CONFIGS[tableName];
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    return new Promise((resolve) => {
      rl.question(
        `\n⚠️  WARNING: This will replace ${itemCount} items in ${this.destinationRegion} ${tableName} table for ${config.hashKey} = "${partitionKeyValue}".\n` +
          `Source: ${SOURCE_REGION} (master)\n` +
          `Destination: ${this.destinationRegion}\n` +
          `Are you sure you want to continue? (yes/no): `,
        (answer) => {
          rl.close();
          resolve(answer.toLowerCase() === "yes" || answer.toLowerCase() === "y");
        }
      );
    });
  }

  /**
   * Main sync function
   */
  async sync(partitionKeyValue, tableName) {
    const config = TABLE_CONFIGS[tableName];
    console.log(`\n=== Simple DynamoDB Sync Tool ===`);
    console.log(`${config.hashKey}: ${partitionKeyValue}`);
    console.log(`Table: ${tableName}`);
    console.log(`Source: ${SOURCE_REGION} (master)`);
    console.log(`Destination: ${this.destinationRegion}\n`);

    if (!TABLE_CONFIGS[tableName]) {
      throw new Error(
        `Unknown table: ${tableName}. Available tables: ${Object.keys(TABLE_CONFIGS).join(", ")}`
      );
    }

    try {
      // Get data from source (master)
      console.log(`Fetching data from ${SOURCE_REGION} (master)...`);
      const sourceData = await this.getTableData(this.sourceClient, tableName, partitionKeyValue);
      console.log(`Found ${sourceData.length} items in source.`);

      if (sourceData.length === 0) {
        console.log("No data found to sync.");
        return;
      }

      // Confirm before proceeding
      const confirmed = await this.confirmSync(partitionKeyValue, tableName, sourceData.length);
      if (!confirmed) {
        console.log("Sync cancelled.");
        return;
      }

      // Clear existing data in destination
      await this.clearTableData(tableName, partitionKeyValue);

      // Write data to destination
      await this.writeTableData(tableName, sourceData);

      console.log(`\n✅ Sync completed successfully!`);
      console.log(
        `Synced ${sourceData.length} items from ${SOURCE_REGION} to ${this.destinationRegion}`
      );
    } catch (error) {
      console.error("\n❌ Sync failed:", error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  if (args.length !== 3) {
    console.log(`
Usage: node scripts/sync-dynamo.js <partitionKeyValue> <tableName> <destinationRegion>

Parameters:
  partitionKeyValue - The value of the partition key to sync data for
  tableName         - The DynamoDB table name to sync
  destinationRegion - The AWS region to sync data to

Available tables and their partition keys:
  sites (siteId), devices (deviceId), components (deviceId), parameters (siteId)
  datadevices (deviceId), commands (timestamp), notifications (id), modes (did)
  baselines (siteId), dailyconsumptions (siteId), dynamictargets (siteId)
  dyanmokeystores (key), processes (processId), users (userId), roles (roleName)
  usercomponentcards (userId_siteId), ShiftProductionData (pk)

Examples:
  node scripts/sync-dynamo.js ssh_site sites us-west-1          # siteId = ssh_site
  node scripts/sync-dynamo.js device123 devices us-west-1       # deviceId = device123
  node scripts/sync-dynamo.js user456 users ap-south-1          # userId = user456
    `);
    process.exit(1);
  }

  // Check AWS credentials first
  try {
    await checkCredentials();
  } catch (error) {
    process.exit(1);
  }

  const [partitionKeyValue, tableName, destinationRegion] = args;

  const syncTool = new SimpleSyncTool(destinationRegion);

  try {
    await syncTool.sync(partitionKeyValue, tableName);
  } catch (error) {
    console.error("Sync failed:", error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = SimpleSyncTool;
