# Simple DynamoDB Sync Tool

This tool provides a simple way to sync DynamoDB table data from us-west-2 (master) to any specified region based on siteId.

## Overview

The sync tool copies data for a specific `siteId` from us-west-2 (master) to any destination region. It supports all the DynamoDB tables in your application and handles the different key structures automatically.

## Supported Tables

The following DynamoDB tables are supported:

### Core Tables

- **sites** - Site configuration data
- **devices** - Device information and configuration
- **components** - Component data and controls
- **parameters** - Device parameters and settings
- **datadevices** - Time-series device data

### Operational Tables

- **commands** - Device commands and execution history
- **notifications** - Alert and notification data
- **modes** - Device mode change history
- **baselines** - Energy consumption baselines
- **dailyconsumptions** - Daily consumption data
- **dynamictargets** - Dynamic target settings

### System Tables

- **dyanmokeystores** - Key-value store for system data
- **processes** - Process definitions
- **users** - User account information
- **roles** - User role definitions
- **usercomponentcards** - User dashboard configurations
- **ShiftProductionData** - Production shift data

## Usage

### Basic Syntax

```bash
node scripts/sync-dynamo.js <siteId> <tableName> <destinationRegion>
```

### Parameters

- `siteId` - The site ID to sync data for (required)
- `tableName` - The DynamoDB table name to sync (required)
- `destinationRegion` - The AWS region to sync data to (required)

## How It Works

1. **Data Retrieval**: The tool fetches all data for the specified `siteId` from us-west-2 (master)
2. **Confirmation**: Asks for user confirmation before proceeding with the sync
3. **Data Clearing**: Removes existing data for the `siteId` in the destination region
4. **Data Writing**: Writes the source data to the destination region in batches

## Safety Features

- **Confirmation Prompt**: Always asks for confirmation before making changes
- **Batch Processing**: Uses DynamoDB batch operations for efficient data transfer
- **Error Handling**: Comprehensive error handling with detailed error messages
- **Filtering**: Only syncs data for the specified `siteId` (where applicable)

## Environment Configuration

The tool always syncs FROM us-west-2 (master) TO the specified destination region.

Make sure your AWS credentials have the necessary permissions for both regions:

- `dynamodb:Query`
- `dynamodb:Scan`
- `dynamodb:BatchWriteItem`
- `dynamodb:BatchGetItem`

## Important Notes

⚠️ **Warning**: This tool will **replace** existing data in the destination region for the specified `siteId`. Always backup important data before running sync operations.

### Tables Without siteId

Some tables (like `users`, `roles`) don't have a `siteId` field. For these tables, the tool will sync **all** data, not just site-specific data. Use with caution.

### Large Datasets

For tables with large amounts of data, the sync process may take some time. The tool shows progress indicators for batch operations.

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure your AWS credentials have DynamoDB permissions for both regions
2. **Table Not Found**: Verify the table name is correct and exists in both regions
3. **Network Timeouts**: For large datasets, consider running during off-peak hours

### Error Messages

- `Unknown table: <tableName>` - The specified table is not in the supported list
- `No data found to sync` - No data exists for the specified siteId in the source region
- `Sync cancelled` - User cancelled the operation at the confirmation prompt

## Support

For issues or questions about the sync tool, please check:

1. This README for common solutions
2. The error messages for specific guidance
3. AWS CloudWatch logs for detailed error information
