# DynamoDB Sync Tool

This tool provides a simple way to sync DynamoDB table data between staging (us-west-1) and master (us-west-2) environments based on siteId.

## Overview

The sync tool can copy data for a specific `siteId` from one environment to another. It supports all the DynamoDB tables in your application and handles the different key structures automatically.

## Supported Tables

The following DynamoDB tables are supported:

### Core Tables
- **sites** - Site configuration data
- **devices** - Device information and configuration
- **components** - Component data and controls
- **parameters** - Device parameters and settings
- **datadevices** - Time-series device data

### Operational Tables
- **commands** - Device commands and execution history
- **notifications** - Alert and notification data
- **modes** - Device mode change history
- **baselines** - Energy consumption baselines
- **dailyconsumptions** - Daily consumption data
- **dynamictargets** - Dynamic target settings

### System Tables
- **dyanmokeystores** - Key-value store for system data
- **processes** - Process definitions
- **users** - User account information
- **roles** - User role definitions
- **usercomponentcards** - User dashboard configurations
- **ShiftProductionData** - Production shift data

## Usage

### Basic Syntax
```bash
node scripts/dynamodb-sync.js <siteId> <tableName> [direction]
```

### Parameters
- `siteId` - The site ID to sync data for (required)
- `tableName` - The DynamoDB table name to sync (required)
- `direction` - Sync direction (optional, default: 'staging-to-master')
  - `staging-to-master` - Copy from staging to master
  - `master-to-staging` - Copy from master to staging

### Examples

#### Sync devices from staging to master
```bash
npm run sync-db ssh_site devices
# or
node scripts/dynamodb-sync.js ssh_site devices staging-to-master
```

#### Sync components from master to staging
```bash
npm run sync-db ssh_site components master-to-staging
```

#### Sync parameters from staging to master
```bash
npm run sync-db ssh_site parameters
```

#### Sync all site data (run multiple commands)
```bash
npm run sync-db ssh_site sites
npm run sync-db ssh_site devices
npm run sync-db ssh_site components
npm run sync-db ssh_site parameters
npm run sync-db ssh_site baselines
npm run sync-db ssh_site dailyconsumptions
```

## How It Works

1. **Data Retrieval**: The tool fetches all data for the specified `siteId` from the source environment
2. **Confirmation**: Asks for user confirmation before proceeding with the sync
3. **Data Clearing**: Removes existing data for the `siteId` in the destination environment
4. **Data Writing**: Writes the source data to the destination environment in batches

## Safety Features

- **Confirmation Prompt**: Always asks for confirmation before making changes
- **Batch Processing**: Uses DynamoDB batch operations for efficient data transfer
- **Error Handling**: Comprehensive error handling with detailed error messages
- **Filtering**: Only syncs data for the specified `siteId` (where applicable)

## Environment Configuration

The tool is configured to work with:
- **Staging Environment**: us-west-1 region
- **Master Environment**: us-west-2 region

Make sure your AWS credentials have the necessary permissions for both regions:
- `dynamodb:Query`
- `dynamodb:Scan`
- `dynamodb:BatchWriteItem`
- `dynamodb:BatchGetItem`

## Important Notes

⚠️ **Warning**: This tool will **replace** existing data in the destination environment for the specified `siteId`. Always backup important data before running sync operations.

### Tables Without siteId
Some tables (like `users`, `roles`) don't have a `siteId` field. For these tables, the tool will sync **all** data, not just site-specific data. Use with caution.

### Large Datasets
For tables with large amounts of data, the sync process may take some time. The tool shows progress indicators for batch operations.

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure your AWS credentials have DynamoDB permissions for both regions
2. **Table Not Found**: Verify the table name is correct and exists in both environments
3. **Network Timeouts**: For large datasets, consider syncing in smaller batches or during off-peak hours

### Error Messages

- `Unknown table: <tableName>` - The specified table is not in the supported list
- `No data found to sync` - No data exists for the specified siteId in the source environment
- `Sync cancelled` - User cancelled the operation at the confirmation prompt

## Advanced Usage

### Programmatic Usage
You can also use the sync tool programmatically:

```javascript
const DynamoDBSyncTool = require('./scripts/dynamodb-sync.js');

const syncTool = new DynamoDBSyncTool();
await syncTool.sync('ssh_site', 'devices', 'staging-to-master');
```

### Bulk Sync Script
For syncing multiple tables, you can create a custom script:

```javascript
const tables = ['sites', 'devices', 'components', 'parameters'];
const siteId = 'ssh_site';

for (const table of tables) {
  await syncTool.sync(siteId, table, 'staging-to-master');
}
```

## Support

For issues or questions about the sync tool, please check:
1. This README for common solutions
2. The error messages for specific guidance
3. AWS CloudWatch logs for detailed error information
