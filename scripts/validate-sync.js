#!/usr/bin/env node

/**
 * DynamoDB Sync Validation Script
 * 
 * This script validates that data has been synced correctly between environments
 * Usage: node scripts/validate-sync.js <siteId> <tableName>
 * 
 * Parameters:
 * - siteId: The site ID to validate sync for
 * - tableName: The DynamoDB table name to validate
 * 
 * Examples:
 * node scripts/validate-sync.js ssh_site devices
 * node scripts/validate-sync.js ssh_site components
 */

const AWS = require('aws-sdk');

// Configuration
const STAGING_REGION = 'us-west-1';
const MASTER_REGION = 'us-west-2';

// Table configurations
const TABLE_CONFIGS = {
  'sites': { hashKey: 'siteId', rangeKey: null },
  'devices': { hashKey: 'deviceId', rangeKey: null },
  'components': { hashKey: 'deviceId', rangeKey: null },
  'parameters': { hashKey: 'siteId', rangeKey: 'deviceId_abbr' },
  'datadevices': { hashKey: 'deviceId', rangeKey: 'timestamp' },
  'commands': { hashKey: 'timestamp', rangeKey: null },
  'notifications': { hashKey: 'id', rangeKey: 'timestamp' },
  'modes': { hashKey: 'did', rangeKey: 'timestamp' },
  'baselines': { hashKey: 'siteId', rangeKey: 'startDate' },
  'dailyconsumptions': { hashKey: 'siteId', rangeKey: 'timestamp' },
  'dynamictargets': { hashKey: 'siteId', rangeKey: 'timestamp' },
  'dyanmokeystores': { hashKey: 'key', rangeKey: null },
  'processes': { hashKey: 'processId', rangeKey: null },
  'users': { hashKey: 'userId', rangeKey: null },
  'roles': { hashKey: 'roleName', rangeKey: null },
  'usercomponentcards': { hashKey: 'userId_siteId', rangeKey: 'id' },
  'ShiftProductionData': { hashKey: 'pk', rangeKey: 'sk' }
};

class SyncValidator {
  constructor() {
    this.stagingClient = new AWS.DynamoDB.DocumentClient({ region: STAGING_REGION });
    this.masterClient = new AWS.DynamoDB.DocumentClient({ region: MASTER_REGION });
  }

  /**
   * Get all items from a table filtered by siteId
   */
  async getTableData(client, tableName, siteId) {
    const config = TABLE_CONFIGS[tableName];
    if (!config) {
      throw new Error(`Unknown table: ${tableName}`);
    }

    let items = [];
    let lastEvaluatedKey = null;

    do {
      const params = {
        TableName: tableName,
        ...(lastEvaluatedKey && { ExclusiveStartKey: lastEvaluatedKey })
      };

      // Add filter expression for siteId if the table has siteId field
      if (this.tableHasSiteId(tableName)) {
        if (config.hashKey === 'siteId') {
          params.KeyConditionExpression = 'siteId = :siteId';
          params.ExpressionAttributeValues = { ':siteId': siteId };
          
          const result = await client.query(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
        } else {
          params.FilterExpression = 'siteId = :siteId';
          params.ExpressionAttributeValues = { ':siteId': siteId };
          
          const result = await client.scan(params).promise();
          items = items.concat(result.Items);
          lastEvaluatedKey = result.LastEvaluatedKey;
        }
      } else {
        const result = await client.scan(params).promise();
        items = items.concat(result.Items);
        lastEvaluatedKey = result.LastEvaluatedKey;
      }
    } while (lastEvaluatedKey);

    return items;
  }

  /**
   * Check if table has siteId field
   */
  tableHasSiteId(tableName) {
    const tablesWithSiteId = [
      'sites', 'devices', 'components', 'parameters', 'datadevices',
      'baselines', 'dailyconsumptions', 'dynamictargets', 'processes',
      'notifications', 'ShiftProductionData'
    ];
    return tablesWithSiteId.includes(tableName);
  }

  /**
   * Create a unique key for an item
   */
  createItemKey(item, config) {
    let key = item[config.hashKey];
    if (config.rangeKey) {
      key += '#' + item[config.rangeKey];
    }
    return key;
  }

  /**
   * Compare two items for differences
   */
  compareItems(item1, item2, config) {
    const differences = [];
    const allKeys = new Set([...Object.keys(item1), ...Object.keys(item2)]);
    
    for (const key of allKeys) {
      if (key === 'createdAt' || key === 'updatedAt') {
        // Skip timestamp fields as they might differ slightly
        continue;
      }
      
      const val1 = item1[key];
      const val2 = item2[key];
      
      if (JSON.stringify(val1) !== JSON.stringify(val2)) {
        differences.push({
          field: key,
          staging: val1,
          master: val2
        });
      }
    }
    
    return differences;
  }

  /**
   * Validate sync between environments
   */
  async validate(siteId, tableName) {
    console.log(`\n=== DynamoDB Sync Validation ===`);
    console.log(`Site ID: ${siteId}`);
    console.log(`Table: ${tableName}`);
    console.log(`Staging Region: ${STAGING_REGION}`);
    console.log(`Master Region: ${MASTER_REGION}\n`);

    if (!TABLE_CONFIGS[tableName]) {
      throw new Error(`Unknown table: ${tableName}. Available tables: ${Object.keys(TABLE_CONFIGS).join(', ')}`);
    }

    const config = TABLE_CONFIGS[tableName];

    try {
      // Get data from both environments
      console.log('Fetching data from staging...');
      const stagingData = await this.getTableData(this.stagingClient, tableName, siteId);
      console.log(`Found ${stagingData.length} items in staging.`);

      console.log('Fetching data from master...');
      const masterData = await this.getTableData(this.masterClient, tableName, siteId);
      console.log(`Found ${masterData.length} items in master.`);

      // Create maps for comparison
      const stagingMap = new Map();
      const masterMap = new Map();

      stagingData.forEach(item => {
        const key = this.createItemKey(item, config);
        stagingMap.set(key, item);
      });

      masterData.forEach(item => {
        const key = this.createItemKey(item, config);
        masterMap.set(key, item);
      });

      // Compare data
      const validation = {
        totalStaging: stagingData.length,
        totalMaster: masterData.length,
        onlyInStaging: [],
        onlyInMaster: [],
        differences: [],
        identical: 0
      };

      // Check items only in staging
      for (const [key, item] of stagingMap) {
        if (!masterMap.has(key)) {
          validation.onlyInStaging.push(key);
        }
      }

      // Check items only in master
      for (const [key, item] of masterMap) {
        if (!stagingMap.has(key)) {
          validation.onlyInMaster.push(key);
        }
      }

      // Check items in both for differences
      for (const [key, stagingItem] of stagingMap) {
        if (masterMap.has(key)) {
          const masterItem = masterMap.get(key);
          const differences = this.compareItems(stagingItem, masterItem, config);
          
          if (differences.length > 0) {
            validation.differences.push({
              key,
              differences
            });
          } else {
            validation.identical++;
          }
        }
      }

      this.printValidationResults(validation);
      return validation;

    } catch (error) {
      console.error('\n❌ Validation failed:', error.message);
      throw error;
    }
  }

  /**
   * Print validation results
   */
  printValidationResults(validation) {
    console.log('\n' + '='.repeat(60));
    console.log('📊 SYNC VALIDATION RESULTS');
    console.log('='.repeat(60));
    
    console.log(`📈 Item Counts:`);
    console.log(`  Staging: ${validation.totalStaging}`);
    console.log(`  Master:  ${validation.totalMaster}`);
    console.log(`  Identical: ${validation.identical}`);
    
    const isInSync = validation.onlyInStaging.length === 0 && 
                     validation.onlyInMaster.length === 0 && 
                     validation.differences.length === 0;

    if (isInSync) {
      console.log('\n✅ ENVIRONMENTS ARE IN SYNC!');
      console.log('All items match between staging and master.');
    } else {
      console.log('\n❌ ENVIRONMENTS ARE NOT IN SYNC!');
      
      if (validation.onlyInStaging.length > 0) {
        console.log(`\n🔍 Items only in staging (${validation.onlyInStaging.length}):`);
        validation.onlyInStaging.slice(0, 10).forEach(key => {
          console.log(`  - ${key}`);
        });
        if (validation.onlyInStaging.length > 10) {
          console.log(`  ... and ${validation.onlyInStaging.length - 10} more`);
        }
      }

      if (validation.onlyInMaster.length > 0) {
        console.log(`\n🔍 Items only in master (${validation.onlyInMaster.length}):`);
        validation.onlyInMaster.slice(0, 10).forEach(key => {
          console.log(`  - ${key}`);
        });
        if (validation.onlyInMaster.length > 10) {
          console.log(`  ... and ${validation.onlyInMaster.length - 10} more`);
        }
      }

      if (validation.differences.length > 0) {
        console.log(`\n🔍 Items with differences (${validation.differences.length}):`);
        validation.differences.slice(0, 5).forEach(({ key, differences }) => {
          console.log(`  - ${key}:`);
          differences.slice(0, 3).forEach(diff => {
            console.log(`    ${diff.field}: staging="${diff.staging}" vs master="${diff.master}"`);
          });
          if (differences.length > 3) {
            console.log(`    ... and ${differences.length - 3} more field differences`);
          }
        });
        if (validation.differences.length > 5) {
          console.log(`  ... and ${validation.differences.length - 5} more items with differences`);
        }
      }
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length !== 2) {
    console.log(`
Usage: node scripts/validate-sync.js <siteId> <tableName>

Parameters:
  siteId     - The site ID to validate sync for
  tableName  - The DynamoDB table name to validate

Available tables:
  ${Object.keys(TABLE_CONFIGS).join(', ')}

Examples:
  node scripts/validate-sync.js ssh_site devices
  node scripts/validate-sync.js ssh_site components
  node scripts/validate-sync.js ssh_site parameters
    `);
    process.exit(1);
  }

  const [siteId, tableName] = args;

  const validator = new SyncValidator();
  
  try {
    const results = await validator.validate(siteId, tableName);
    
    // Exit with error code if not in sync
    const isInSync = results.onlyInStaging.length === 0 && 
                     results.onlyInMaster.length === 0 && 
                     results.differences.length === 0;
    
    process.exit(isInSync ? 0 : 1);
  } catch (error) {
    console.error('Validation failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = SyncValidator;
